#!/bin/bash

echo "=== Debugging Setup ==="

# Check Node.js
if command -v node &> /dev/null; then
    echo "✓ Node.js found: $(node --version)"
else
    echo "✗ Node.js not found"
    exit 1
fi

# Check npm
if command -v npm &> /dev/null; then
    echo "✓ npm found: $(npm --version)"
else
    echo "✗ npm not found"
    exit 1
fi

# Create directories
echo "Creating directories..."
mkdir -p automation/{scripts,reports,test-files,step-definitions,support,config,features}

# Install basic dependencies
echo "Installing dependencies..."
npm install --save-dev @cucumber/cucumber chai axios form-data

echo "Setup completed successfully!"