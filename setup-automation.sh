#!/bin/bash

echo "Setting up API Automation Framework..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
npm install

# Install required packages for automation
echo "Installing automation dependencies..."
npm install --save-dev @cucumber/cucumber chai axios form-data multiple-cucumber-html-reporter

# Create directory structure
echo "Creating directory structure..."
mkdir -p automation/scripts
mkdir -p automation/reports
mkdir -p automation/test-files
mkdir -p automation/step-definitions
mkdir -p automation/support
mkdir -p automation/config
mkdir -p automation/features

# Create setup-directories.sh if it doesn't exist
if [ ! -f "automation/scripts/setup-directories.sh" ]; then
    cat > automation/scripts/setup-directories.sh << 'EOF'
#!/bin/bash

# Create necessary directories
mkdir -p automation/reports
mkdir -p automation/test-files
mkdir -p automation/step-definitions
mkdir -p automation/support
mkdir -p automation/config
mkdir -p automation/features

# Make scripts executable
chmod +x automation/scripts/run-tests.sh
chmod +x automation/scripts/setup-directories.sh

echo "Directory structure created successfully!"
EOF
fi

# Make scripts executable
chmod +x automation/scripts/setup-directories.sh
chmod +x automation/scripts/run-tests.sh
chmod +x setup-automation.sh

# Run directory setup
bash automation/scripts/setup-directories.sh

echo "Setup complete! You can now run tests with: npm run test:api"
