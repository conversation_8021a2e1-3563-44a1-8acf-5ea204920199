const reporter = require('multiple-cucumber-html-reporter');
const fs = require('fs');
const path = require('path');

// Read cucumber report
const reportPath = path.join(__dirname, '../reports/cucumber-report.json');
let reportData = [];

if (fs.existsSync(reportPath)) {
  reportData = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
}

// Generate multi-cucumber HTML report
reporter.generate({
  jsonDir: 'automation/reports/',
  reportPath: 'automation/reports/',
  reportName: 'API Automation Test Report',
  pageTitle: 'Health Solutions Automation API Tests',
  displayDuration: true,
  displayReportTime: true,
  showAttachments: true,
  metadata: {
    browser: {
      name: 'API Tests',
      version: '1.0'
    },
    device: 'Local Machine',
    platform: {
      name: 'Node.js',
      version: process.version
    }
  },
  customData: {
    title: 'Test Execution Info',
    data: [
      { label: 'Project', value: 'Health Solutions Automation API' },
      { label: 'Environment', value: process.env.NODE_ENV || 'development' },
      { label: 'Execution Date', value: new Date().toISOString() },
      { label: 'Test Types', value: 'Functional, Regression, Boundary, Negative' },
      { label: 'Request/Response', value: 'Captured in attachments' }
    ]
  }
});

console.log('Multi-cucumber HTML report generated successfully!');
console.log('Request/Response data will be visible in the HTML report attachments');
