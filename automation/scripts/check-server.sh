#!/bin/bash

echo "Checking if API server is running..."

# Check if server is running on port 3000
if curl -s http://localhost:3000/microservices/automation/health > /dev/null 2>&1; then
    echo "✅ API server is running on http://localhost:3000"
    exit 0
else
    echo "❌ API server is not running on http://localhost:3000"
    echo "Please start the server with: npm start"
    echo "Or run in development mode: npm run start:dev"
    exit 1
fi