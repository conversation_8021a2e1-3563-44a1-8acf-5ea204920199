#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  API Automation Test Execution${NC}"
echo -e "${BLUE}========================================${NC}"

# Check if API server is running - try multiple endpoints
echo -e "${YELLOW}Checking API server status...${NC}"

# Try different possible health check endpoints
HEALTH_ENDPOINTS=(
    "http://localhost:3000/microservices/automation/api/healthcheck"
    "http://localhost:3000/microservices/automation/healthcheck"
    "http://localhost:3000/microservices/automation/health"
    "http://localhost:3000/health"
)

SERVER_RUNNING=false
WORKING_ENDPOINT=""

for endpoint in "${HEALTH_ENDPOINTS[@]}"; do
    if curl -s "$endpoint" > /dev/null 2>&1; then
        SERVER_RUNNING=true
        WORKING_ENDPOINT="$endpoint"
        break
    fi
done

if [ "$SERVER_RUNNING" = false ]; then
    echo -e "${RED}❌ API server is not running on http://localhost:3000${NC}"
    echo -e "${YELLOW}Tried endpoints:${NC}"
    for endpoint in "${HEALTH_ENDPOINTS[@]}"; do
        echo -e "  - $endpoint"
    done
    echo -e "${YELLOW}Please start the server first:${NC}"
    echo -e "  npm start"
    echo -e "  or"
    echo -e "  npm run start:dev"
    exit 1
else
    echo -e "${GREEN}✅ API server is running at: $WORKING_ENDPOINT${NC}"
fi

# Create reports directory
mkdir -p automation/reports
mkdir -p automation/test-files

# Clean previous reports
rm -f automation/reports/*.json
rm -f automation/reports/*.html
rm -f automation/package.json

# Check if node modules exist
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing dependencies...${NC}"
    npm install
fi

echo -e "${YELLOW}Starting API automation tests...${NC}"

# Run cucumber tests from project root
cd "$(dirname "$0")/../.."

npx cucumber-js automation/features \
  --require automation/step-definitions \
  --format json:automation/reports/cucumber-report.json \
  --format html:automation/reports/cucumber-report.html \
  --format progress-bar \
  --tags "@Regression"

TEST_EXIT_CODE=$?
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✓ All tests passed successfully!${NC}"
    TEST_STATUS="PASSED"
else
    echo -e "${RED}✗ Some tests failed! Exit code: $TEST_EXIT_CODE${NC}"
    TEST_STATUS="FAILED"
fi

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  Test Execution Summary${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "Status: ${TEST_STATUS}"
echo -e "Reports generated in: automation/reports/"
echo -e "${BLUE}========================================${NC}"

if [ -d "automation/reports" ]; then
    echo -e "${YELLOW}Generated files:${NC}"
    ls -la automation/reports/
fi

exit $TEST_EXIT_CODE
