const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

class ApiHelper {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.lastResponse = null;
  }

  async post(endpoint, data, headers = {}) {
    try {
      const response = await axios.post(`${this.baseUrl}${endpoint}`, data, {
        headers: { 'Content-Type': 'application/json', ...headers },
        validateStatus: () => true,
        timeout: 30000
      });
      this.lastResponse = response;
      return response;
    } catch (error) {
      console.error(`POST ${endpoint} failed:`, error.message);
      this.lastResponse = { 
        status: error.response?.status || 500, 
        data: { error: error.message } 
      };
      return this.lastResponse;
    }
  }

  getLastResponse() {
    return this.lastResponse;
  }
}

module.exports = ApiHelper;
