[{"description": "  As a test automation engineer\n  I want to manage API execution records\n  So that I can track API test results", "elements": [{"description": "", "id": "api-execution-record-operations;create-api-execution-record-with-valid-data", "keyword": "<PERSON><PERSON><PERSON>", "line": 11, "name": "Create API execution record with valid data", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 391250}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 11279916}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtRkV1QW5jVG43YStJSjMxOTFmNm5QU09ucXdBXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xNjc3MjI5NTgKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 12, "name": "I create an API execution record with valid data", "match": {"location": "automation/step-definitions/api-execution-steps.js:6"}, "result": {"status": "passed", "duration": 3296417}, "embeddings": [{"data": "8J+TpCBSRVFVRVNUOgp7CiAgIm1ldGhvZCI6ICJQT1NUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpLWV4ZWN1dGlvbi1yZWNvcmRzIiwKICAiaGVhZGVycyI6IHsKICAgICJDb250ZW50LVR5cGUiOiAiYXBwbGljYXRpb24vanNvbiIKICB9LAogICJib2R5IjogewogICAgImRhdGEiOiB7CiAgICAgICJhcGluYW1lIjogIlRlc3QgQVBJIiwKICAgICAgInN0YXR1cyI6ICJQQVNTRUQiLAogICAgICAidGVzdGV4ZWN1dGlvbmRhdGVhbmR0aW1lIjogIjIwMjUtMDctMTdUMTg6MTg6MjAuMzA3WiIsCiAgICAgICJzdGF0dXNjb2RlIjogIjIwMCIsCiAgICAgICJzdGF0dXNkZXNjIjogIlN1Y2Nlc3MiLAogICAgICAicmVzcG9uc2UiOiAiVGVzdCByZXNwb25zZSIsCiAgICAgICJyZXNwb25zZV9oZWFkZXJzIjogIkNvbnRlbnQtVHlwZTogYXBwbGljYXRpb24vanNvbiIsCiAgICAgICJyZXNwb25zZXRpbWUiOiAiMTUwbXMiCiAgICB9CiAgfQp9", "mime_type": "application/json"}, {"data": "4p2MIEVSUk9SOgp7CiAgIm1lc3NhZ2UiOiAiUmVxdWVzdCBmYWlsZWQgd2l0aCBzdGF0dXMgY29kZSA0MDQiLAogICJzdGF0dXMiOiA0MDQsCiAgInN0YXR1c1RleHQiOiAiTm90IEZvdW5kIiwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkNhbm5vdCBQT1NUIC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpLWV4ZWN1dGlvbi1yZWNvcmRzIiwKICAgICJlcnJvciI6ICJOb3QgRm91bmQiLAogICAgInN0YXR1c0NvZGUiOiA0MDQKICB9LAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjExMCIsCiAgICAiZXRhZyI6ICJXL1wiNmUtRjRqaDAyWVlteWxLUlZJOUhzT2JWWkt6Q0RjXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfQp9", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Then ", "line": 13, "name": "the response status should be 201", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 517458, "error_message": "AssertionError\n    + expected - actual\n\n    -404\n    +201\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1GRXVBbmNUbjdhK0lKMzE5MWY2blBTT25xd0FcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjE2NzcyMjk1OAogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 14, "name": "the response should contain API execution record data", "match": {"location": "automation/step-definitions/api-execution-steps.js:134"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 15, "name": "the API record should have correct apiname \"test3\"", "match": {"location": "automation/step-definitions/api-execution-steps.js:140"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 16, "name": "the API record should have correct status \"PASSED\"", "match": {"location": "automation/step-definitions/api-execution-steps.js:145"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 10}, {"name": "@Functional", "line": 10}], "type": "scenario"}, {"description": "", "id": "api-execution-record-operations;create-api-execution-record-with-missing-required-fields", "keyword": "<PERSON><PERSON><PERSON>", "line": 19, "name": "Create API execution record with missing required fields", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 16416}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 1146916}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtSHZTY3hLRjNGd1FWSzFyaVhDMnZvd3J6dUJBXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xNzU5NTI5MTcKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 20, "name": "I create an API execution record with empty apiname", "match": {"location": "automation/step-definitions/api-execution-steps.js:65"}, "result": {"status": "passed", "duration": 1247208}}, {"arguments": [], "keyword": "Then ", "line": 21, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 168957, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +400\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1IdlNjeEtGM0Z3UVZLMXJpWEMydm93cnp1QkFcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjE3NTk1MjkxNwogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 22, "name": "the response should contain validation error message", "match": {"location": "automation/step-definitions/common-steps.js:80"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Negative", "line": 18}, {"name": "@Validation", "line": 18}], "type": "scenario"}, {"description": "", "id": "api-execution-record-operations;retrieve-all-api-execution-records", "keyword": "<PERSON><PERSON><PERSON>", "line": 25, "name": "Retrieve all API execution records", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 22084}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 1145290}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0MiIsCiAgICAiZXRhZyI6ICJXL1wiMmEtTUs2a09tQkZYcmliZkl4eitka25xWkJSd0dVXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xNzk0OTc1CiAgfQp9", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 26, "name": "I have created an API execution record", "match": {"location": "automation/step-definitions/api-execution-steps.js:77"}, "result": {"status": "passed", "duration": 1066207}}, {"arguments": [], "keyword": "When ", "line": 27, "name": "I retrieve all API execution records", "match": {"location": "automation/step-definitions/api-execution-steps.js:89"}, "result": {"status": "passed", "duration": 1255083}, "embeddings": [{"data": "8J+TpCBSRVFVRVNUOgp7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGktZXhlY3V0aW9uLXJlY29yZHMiLAogICJoZWFkZXJzIjoge30KfQ==", "mime_type": "application/json"}, {"data": "4p2MIEVSUk9SOgp7CiAgIm1lc3NhZ2UiOiAiUmVxdWVzdCBmYWlsZWQgd2l0aCBzdGF0dXMgY29kZSA0MDQiLAogICJzdGF0dXMiOiA0MDQsCiAgInN0YXR1c1RleHQiOiAiTm90IEZvdW5kIiwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkNhbm5vdCBHRVQgL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGktZXhlY3V0aW9uLXJlY29yZHMiLAogICAgImVycm9yIjogIk5vdCBGb3VuZCIsCiAgICAic3RhdHVzQ29kZSI6IDQwNAogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Then ", "line": 28, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 104041, "error_message": "AssertionError\n    + expected - actual\n\n    -404\n    +200\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQyIiwKICAgICJldGFnIjogIlcvXCIyYS1NSzZrT21CRlhyaWJmSXh6K2RrbnFaQlJ3R1VcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjE3OTQ5NzUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 29, "name": "the response should contain array of API execution records", "match": {"location": "automation/step-definitions/api-execution-steps.js:150"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 24}, {"name": "@Functional", "line": 24}], "type": "scenario"}, {"description": "", "id": "api-execution-record-operations;update-api-execution-record", "keyword": "<PERSON><PERSON><PERSON>", "line": 32, "name": "Update API execution record", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 19375}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 763582}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtRUdUOWpRN0JGOXY1YUwyVm9xODhtYjRLcGY4XCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xODM2MjI3MDgKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 33, "name": "I have created an API execution record with sno", "match": {"location": "automation/step-definitions/api-execution-steps.js:83"}, "result": {"status": "failed", "duration": 1112500, "error_message": "TypeError: Cannot read properties of undefined (reading 'sno')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/api-execution-steps.js:86:49)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}}, {"arguments": [], "keyword": "When ", "line": 34, "name": "I update the API execution record with new status \"FAILED\"", "match": {"location": "automation/step-definitions/api-execution-steps.js:129"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 35, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 36, "name": "the updated API record should have status \"FAILED\"", "match": {"location": "automation/step-definitions/api-execution-steps.js:155"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 31}, {"name": "@Functional", "line": 31}], "type": "scenario"}, {"description": "", "id": "api-execution-record-operations;create-api-execution-record-with-various-date-formats", "keyword": "Scenario Outline", "line": 45, "name": "Create API execution record with various date formats", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 17999}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 1095749}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0MyIsCiAgICAiZXRhZyI6ICJXL1wiMmItbU4vZG1SazBZM0lncE1HR3M0TlFrSzRvazBnXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xODYzNjQ3NQogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 40, "name": "I create an API execution record with testexecutiondateandtime \"2025-04-17 11:07:23\"", "match": {"location": "automation/step-definitions/api-execution-steps.js:71"}, "result": {"status": "passed", "duration": 1178957}}, {"arguments": [], "keyword": "Then ", "line": 41, "name": "the response status should be 201", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 106625, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +201\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQzIiwKICAgICJldGFnIjogIlcvXCIyYi1tTi9kbVJrMFkzSWdwTUdHczROUWtLNG9rMGdcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjE4NjM2NDc1CiAgfQp9", "mime_type": "application/json"}]}], "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 38}, {"name": "@DateTime", "line": 38}], "type": "scenario"}, {"description": "", "id": "api-execution-record-operations;create-api-execution-record-with-various-date-formats", "keyword": "Scenario Outline", "line": 46, "name": "Create API execution record with various date formats", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 13208}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 879582}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtR01yUXNVNjVldnRQY3VUTDJqK3hFbytpYlJrXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xODkyNjEwNDIKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 40, "name": "I create an API execution record with testexecutiondateandtime \"2025-12-31 23:59:59\"", "match": {"location": "automation/step-definitions/api-execution-steps.js:71"}, "result": {"status": "passed", "duration": 704749}}, {"arguments": [], "keyword": "Then ", "line": 41, "name": "the response status should be 201", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 149166, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +201\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1HTXJRc1U2NWV2dFBjdVRMMmoreEVvK2liUmtcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjE4OTI2MTA0MgogIH0KfQ==", "mime_type": "application/json"}]}], "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 38}, {"name": "@DateTime", "line": 38}], "type": "scenario"}, {"description": "", "id": "api-execution-record-operations;create-api-execution-record-with-various-date-formats", "keyword": "Scenario Outline", "line": 47, "name": "Create API execution record with various date formats", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 17917}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 750833}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0MyIsCiAgICAiZXRhZyI6ICJXL1wiMmItMnl3OVZmRzFZODlTU2NVaW1HMW5JSkRsZUFjXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xOTEzMzQyNQogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 40, "name": "I create an API execution record with testexecutiondateandtime \"invalid_date\"", "match": {"location": "automation/step-definitions/api-execution-steps.js:71"}, "result": {"status": "passed", "duration": 1000833}}, {"arguments": [], "keyword": "Then ", "line": 41, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 83624, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +400\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQzIiwKICAgICJldGFnIjogIlcvXCIyYi0yeXc5VmZHMVk4OVNTY1VpbUcxbklKRGxlQWNcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjE5MTMzNDI1CiAgfQp9", "mime_type": "application/json"}]}], "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 38}, {"name": "@DateTime", "line": 38}], "type": "scenario"}, {"description": "", "id": "api-execution-record-operations;create-api-execution-record-with-various-date-formats", "keyword": "Scenario Outline", "line": 48, "name": "Create API execution record with various date formats", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 9833}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 1686874}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMteFhYVEJnNjhRZU80b1hqQnlwNWdQOTZSYWE0XCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xOTM3NDA2MjUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 40, "name": "I create an API execution record with testexecutiondateandtime \"2025-13-01 11:07:23\"", "match": {"location": "automation/step-definitions/api-execution-steps.js:71"}, "result": {"status": "passed", "duration": 1007791}}, {"arguments": [], "keyword": "Then ", "line": 41, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 77417, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +400\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy14WFhUQmc2OFFlTzRvWGpCeXA1Z1A5NlJhYTRcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjE5Mzc0MDYyNQogIH0KfQ==", "mime_type": "application/json"}]}], "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 38}, {"name": "@DateTime", "line": 38}], "type": "scenario"}], "id": "api-execution-record-operations", "line": 2, "keyword": "Feature", "name": "API Execution Record Operations", "tags": [{"name": "@APIExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}], "uri": "automation/features/api-execution-record.feature"}, {"description": "  As a test automation engineer\n  I want to manage appointment records\n  So that I can track appointment booking data", "elements": [{"description": "", "id": "appointment-record-operations;create-appointment-record-with-valid-data", "keyword": "<PERSON><PERSON><PERSON>", "line": 11, "name": "Create appointment record with valid data", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 50334}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 641625}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtenFjNGtqYWYzazVLK0pFUlZ1QXlQWm9rZWRRXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xOTY4MTY1NDIKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 12, "name": "I create an appointment record with valid data", "match": {"location": "automation/step-definitions/appointment-steps.js:5"}, "result": {"status": "failed", "duration": 84374, "error_message": "TypeError: Cannot read properties of undefined (reading 'valid')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/appointment-steps.js:6:47)"}}, {"arguments": [], "keyword": "Then ", "line": 13, "name": "the response status should be 201", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 14, "name": "the response should contain appointment record data", "match": {"location": "automation/step-definitions/appointment-steps.js:58"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 15, "name": "the appointment should have correct confirmationcode", "match": {"location": "automation/step-definitions/appointment-steps.js:64"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 16, "name": "the appointment should have correct lob \"mc\"", "match": {"location": "automation/step-definitions/appointment-steps.js:69"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@AppointmentRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 10}, {"name": "@Functional", "line": 10}], "type": "scenario"}, {"description": "", "id": "appointment-record-operations;create-appointment-record-with-missing-confirmation-code", "keyword": "<PERSON><PERSON><PERSON>", "line": 19, "name": "Create appointment record with missing confirmation code", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 7625}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 811541}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtbk9SSEVPZzhLU25ESW9BZGFDckZHdDFsZTE4XCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xOTgxNjE2MjUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 20, "name": "I create an appointment record with empty confirmationcode", "match": {"location": "automation/step-definitions/appointment-steps.js:10"}, "result": {"status": "failed", "duration": 75374, "error_message": "TypeError: Cannot read properties of undefined (reading 'valid')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/appointment-steps.js:11:48)"}}, {"arguments": [], "keyword": "Then ", "line": 21, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 22, "name": "the response should contain validation error message", "match": {"location": "automation/step-definitions/common-steps.js:80"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@AppointmentRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Negative", "line": 18}, {"name": "@Validation", "line": 18}], "type": "scenario"}, {"description": "", "id": "appointment-record-operations;retrieve-appointments-by-lob-and-clinic-type", "keyword": "<PERSON><PERSON><PERSON>", "line": 25, "name": "Retrieve appointments by LOB and clinic type", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 8083}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 801791}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0MiIsCiAgICAiZXRhZyI6ICJXL1wiMmEteHJvSGlnV1FRRVhnNGJLRTJMRG5tc0Rid3lZXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4xOTk0NDM1CiAgfQp9", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 26, "name": "I have created an appointment record", "match": {"location": "automation/step-definitions/appointment-steps.js:16"}, "result": {"status": "failed", "duration": 57374, "error_message": "TypeError: Cannot read properties of undefined (reading 'valid')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/appointment-steps.js:17:47)"}}, {"arguments": [], "keyword": "When ", "line": 27, "name": "I search appointments by lob \"mc\" and clinictype \"minuteclinic\"", "match": {"location": "automation/step-definitions/appointment-steps.js:35"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 28, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 29, "name": "the response should contain matching appointment records", "match": {"location": "automation/step-definitions/appointment-steps.js:74"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@AppointmentRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 24}, {"name": "@Functional", "line": 24}], "type": "scenario"}, {"description": "", "id": "appointment-record-operations;update-appointment-record", "keyword": "<PERSON><PERSON><PERSON>", "line": 32, "name": "Update appointment record", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 7042}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 1277207}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtc2FKTGFQV2EvSjh1dklWcldPVTJlbDk3ZFRBXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMDAzNjM1NDIKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 33, "name": "I have created an appointment record with confirmationcode", "match": {"location": "automation/step-definitions/appointment-steps.js:22"}, "result": {"status": "failed", "duration": 111500, "error_message": "TypeError: Cannot read properties of undefined (reading 'valid')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/appointment-steps.js:23:47)"}}, {"arguments": [], "keyword": "When ", "line": 34, "name": "I update the appointment record with new cancel_status \"Canceled\"", "match": {"location": "automation/step-definitions/appointment-steps.js:53"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 35, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 36, "name": "the updated appointment should have cancel_status \"Canceled\"", "match": {"location": "automation/step-definitions/appointment-steps.js:80"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@AppointmentRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 31}, {"name": "@Functional", "line": 31}], "type": "scenario"}, {"description": "", "id": "appointment-record-operations;search-appointments-with-date-comparisons", "keyword": "Scenario Outline", "line": 46, "name": "Search appointments with date comparisons", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 12458}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 634416}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtc2hHOEY5a0FEZjk1UHRhVUhzQUZGbEswanBFXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMDIxNDY2NjcKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 40, "name": "I have created an appointment record with date \"2025-06-26 07:17:46\"", "match": {"location": "automation/step-definitions/appointment-steps.js:28"}, "result": {"status": "failed", "duration": 82291, "error_message": "TypeError: Cannot read properties of undefined (reading 'valid')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/appointment-steps.js:29:41)"}}, {"arguments": [], "keyword": "When ", "line": 41, "name": "I search appointments with date \"2025-06-25 07:17:46\" and comparison \"gt\"", "match": {"location": "automation/step-definitions/appointment-steps.js:44"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 42, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@AppointmentRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 38}, {"name": "@DateComparison", "line": 38}], "type": "scenario"}, {"description": "", "id": "appointment-record-operations;search-appointments-with-date-comparisons", "keyword": "Scenario Outline", "line": 47, "name": "Search appointments with date comparisons", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 12999}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 788957}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMteVl4WXIxRU1FcnBOazRzUFdabDAzUGRHUFpZXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMDMyNzE1NDIKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 40, "name": "I have created an appointment record with date \"2025-06-26 07:17:46\"", "match": {"location": "automation/step-definitions/appointment-steps.js:28"}, "result": {"status": "failed", "duration": 70875, "error_message": "TypeError: Cannot read properties of undefined (reading 'valid')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/appointment-steps.js:29:41)"}}, {"arguments": [], "keyword": "When ", "line": 41, "name": "I search appointments with date \"2025-06-25 07:17:46\" and comparison \"lt\"", "match": {"location": "automation/step-definitions/appointment-steps.js:44"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 42, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@AppointmentRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 38}, {"name": "@DateComparison", "line": 38}], "type": "scenario"}, {"description": "", "id": "appointment-record-operations;search-appointments-with-date-comparisons", "keyword": "Scenario Outline", "line": 48, "name": "Search appointments with date comparisons", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 5334}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 926874}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtVm5SN1NPQVRQcm1qeUQweUhreEdnWkF0anF3XCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMDQ0ODI2MjUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 40, "name": "I have created an appointment record with date \"2025-06-26 07:17:46\"", "match": {"location": "automation/step-definitions/appointment-steps.js:28"}, "result": {"status": "failed", "duration": 71958, "error_message": "TypeError: Cannot read properties of undefined (reading 'valid')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/appointment-steps.js:29:41)"}}, {"arguments": [], "keyword": "When ", "line": 41, "name": "I search appointments with date \"2025-06-25 07:17:46\" and comparison \"eq\"", "match": {"location": "automation/step-definitions/appointment-steps.js:44"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 42, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@AppointmentRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 38}, {"name": "@DateComparison", "line": 38}], "type": "scenario"}], "id": "appointment-record-operations", "line": 2, "keyword": "Feature", "name": "Appointment Record Operations", "tags": [{"name": "@AppointmentRecord", "line": 1}, {"name": "@Regression", "line": 1}], "uri": "automation/features/appointment-record.feature"}, {"description": "  As a test automation engineer\n  I want to perform CRUD operations on execution records\n  So that I can manage test execution data effectively", "elements": [{"description": "", "id": "execution-record-api-operations;create-execution-record-with-valid-data", "keyword": "<PERSON><PERSON><PERSON>", "line": 11, "name": "Create execution record with valid data", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 7958}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 597082}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtdi8wNDd3RWp2UFZvZEpNQi9LVlY5NDNST0RzXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMDU2MTI5MTcKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 12, "name": "I create an execution record with valid data", "match": {"location": "automation/step-definitions/execution-record-steps.js:5"}, "result": {"status": "passed", "duration": 827208}}, {"arguments": [], "keyword": "Then ", "line": 13, "name": "the response status should be 201", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 80166, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +201\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy12LzA0N3dFanZQVm9kSk1CL0tWVjk0M1JPRHNcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIwNTYxMjkxNwogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 14, "name": "the response should contain execution record data", "match": {"location": "automation/step-definitions/execution-record-steps.js:54"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 15, "name": "the execution record should have correct parenttag", "match": {"location": "automation/step-definitions/execution-record-steps.js:60"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 16, "name": "the execution record should have correct status", "match": {"location": "automation/step-definitions/execution-record-steps.js:65"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 10}, {"name": "@Functional", "line": 10}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;create-execution-record-with-missing-required-fields", "keyword": "<PERSON><PERSON><PERSON>", "line": 19, "name": "Create execution record with missing required fields", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 7042}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 666541}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtSDh5ZG95TktkTWlPREd2Tzhja3RZQzJRYlpjXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMDc2MDEzMzMKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 20, "name": "I create an execution record with empty parenttag", "match": {"location": "automation/step-definitions/execution-record-steps.js:10"}, "result": {"status": "passed", "duration": 702875}}, {"arguments": [], "keyword": "Then ", "line": 21, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 76124, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +400\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1IOHlkb3lOS2RNaU9ER3ZPOGNrdFlDMlFiWmNcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIwNzYwMTMzMwogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 22, "name": "the response should contain validation error message", "match": {"location": "automation/step-definitions/common-steps.js:80"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Negative", "line": 18}, {"name": "@Validation", "line": 18}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;create-execution-record-with-invalid-status", "keyword": "<PERSON><PERSON><PERSON>", "line": 25, "name": "Create execution record with invalid status", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 5708}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 730000}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtRVRGVWR4THo3OXhjVkV1ZTRSeXp5ZDh3QTMwXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMDk1NTAyMDgKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 26, "name": "I create an execution record with invalid status \"INVALID_STATUS\"", "match": {"location": "automation/step-definitions/execution-record-steps.js:16"}, "result": {"status": "passed", "duration": 899124}}, {"arguments": [], "keyword": "Then ", "line": 27, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 81334, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +400\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1FVEZVZHhMejc5eGNWRXVlNFJ5enlkOHdBMzBcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIwOTU1MDIwOAogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 28, "name": "the response should contain validation error for status field", "match": {"location": "automation/step-definitions/execution-record-steps.js:86"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Negative", "line": 24}, {"name": "@Validation", "line": 24}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;retrieve-all-execution-records", "keyword": "<PERSON><PERSON><PERSON>", "line": 31, "name": "Retrieve all execution records", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 9250}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 851874}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtaVk1R09EVVJrTnFPdUMzemxuYnY2Y1dFNFlNXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMTE4MzM0MTcKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 32, "name": "I have created an execution record", "match": {"location": "automation/step-definitions/execution-record-steps.js:28"}, "result": {"status": "passed", "duration": 925457}}, {"arguments": [], "keyword": "When ", "line": 33, "name": "I retrieve all execution records", "match": {"location": "automation/step-definitions/execution-record-steps.js:40"}, "result": {"status": "failed", "duration": 84499, "error_message": "TypeError: this.apiHelper.get is not a function\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/execution-record-steps.js:41:24)"}}, {"arguments": [], "keyword": "Then ", "line": 34, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 35, "name": "the response should contain array of execution records", "match": {"location": "automation/step-definitions/execution-record-steps.js:70"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 30}, {"name": "@Functional", "line": 30}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;retrieve-execution-records-by-criteria", "keyword": "<PERSON><PERSON><PERSON>", "line": 38, "name": "Retrieve execution records by criteria", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 6584}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 697083}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtV202RWUrVVlLVWV5dThhQnc2WEQwL2FtUVlnXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMTQwOTg3OTIKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 39, "name": "I have created an execution record", "match": {"location": "automation/step-definitions/execution-record-steps.js:28"}, "result": {"status": "passed", "duration": 718375}}, {"arguments": [], "keyword": "When ", "line": 40, "name": "I search execution records by parenttag \"@AI_MCCore_Dailyrun_EntryPoint_One\"", "match": {"location": "automation/step-definitions/execution-record-steps.js:44"}, "result": {"status": "passed", "duration": 744958}}, {"arguments": [], "keyword": "Then ", "line": 41, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "passed", "duration": 60584}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1XbTZFZStVWUtVZXl1OGFCdzZYRDAvYW1RWWdcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIxNDA5ODc5MgogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 42, "name": "the response should contain matching execution records", "match": {"location": "automation/step-definitions/execution-record-steps.js:75"}, "result": {"status": "failed", "duration": 363625, "error_message": "AssertionError: expected { …(3) } to be an array\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/execution-record-steps.js:77:31)"}}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 37}, {"name": "@Functional", "line": 37}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;retrieve-execution-records-with-non-existent-criteria", "keyword": "<PERSON><PERSON><PERSON>", "line": 45, "name": "Retrieve execution records with non-existent criteria", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 5625}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 764833}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtZ0ZvUUFvSVhkMWtNM25OSTJKTVNVclUydjkwXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMTczMDQ4MzMKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 46, "name": "I search execution records by parenttag \"NON_EXISTENT_TAG\"", "match": {"location": "automation/step-definitions/execution-record-steps.js:44"}, "result": {"status": "passed", "duration": 647374}}, {"arguments": [], "keyword": "Then ", "line": 47, "name": "the response status should be 404", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 77999, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +404\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1nRm9RQW9JWGQxa00zbk5JMkpNU1VyVTJ2OTBcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIxNzMwNDgzMwogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 48, "name": "the response should contain not found error message", "match": {"location": "automation/step-definitions/common-steps.js:86"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Negative", "line": 44}, {"name": "@NotFound", "line": 44}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;update-execution-record-with-valid-data", "keyword": "<PERSON><PERSON><PERSON>", "line": 51, "name": "Update execution record with valid data", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 5875}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 620999}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtY0VmaUhFNEsxelNPM3J2Zmt0VU1kZ3lhNXRNXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMTkxNDkyMDgKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "Given ", "line": 52, "name": "I have created an execution record with id", "match": {"location": "automation/step-definitions/execution-record-steps.js:34"}, "result": {"status": "failed", "duration": 695625, "error_message": "TypeError: Cannot read properties of undefined (reading 'id')\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/execution-record-steps.js:37:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}}, {"arguments": [], "keyword": "When ", "line": 53, "name": "I update the execution record with new status \"FAILED\"", "match": {"location": "automation/step-definitions/execution-record-steps.js:49"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 54, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 55, "name": "the updated record should have status \"FAILED\"", "match": {"location": "automation/step-definitions/execution-record-steps.js:81"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 50}, {"name": "@Functional", "line": 50}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;create-execution-record-with-boundary-values", "keyword": "Scenario Outline", "line": 64, "name": "Create execution record with boundary values", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 6916}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 702791}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtdGR1aUc1cHJCeDBpMHp0emVyV2VKdk5DZ0VrXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMjA3NDczNzUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 59, "name": "I create an execution record with totaltimeinseconds 0", "match": {"location": "automation/step-definitions/execution-record-steps.js:22"}, "result": {"status": "passed", "duration": 1413125}}, {"arguments": [], "keyword": "Then ", "line": 60, "name": "the response status should be 201", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 102499, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +201\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy10ZHVpRzVwckJ4MGkwenR6ZXJXZUp2TkNnRWtcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIyMDc0NzM3NQogIH0KfQ==", "mime_type": "application/json"}]}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 57}, {"name": "@Validation", "line": 57}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;create-execution-record-with-boundary-values", "keyword": "Scenario Outline", "line": 65, "name": "Create execution record with boundary values", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 4917}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 661999}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtSzJxRWsxNUJubjZKcXVBUXZqTlp3SHNNOWg4XCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMjMzMjcyMDgKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 59, "name": "I create an execution record with totaltimeinseconds 1", "match": {"location": "automation/step-definitions/execution-record-steps.js:22"}, "result": {"status": "passed", "duration": 544125}}, {"arguments": [], "keyword": "Then ", "line": 60, "name": "the response status should be 201", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 74084, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +201\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1LMnFFazE1Qm5uNkpxdUFRdmpOWndIc005aDhcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIyMzMyNzIwOAogIH0KfQ==", "mime_type": "application/json"}]}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 57}, {"name": "@Validation", "line": 57}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;create-execution-record-with-boundary-values", "keyword": "Scenario Outline", "line": 66, "name": "Create execution record with boundary values", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 4916}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 458667}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtK2ZKZTliYXQ5L3R3dHQydGEyVkYrODdhVEF3XCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMjQ4MTEyOTIKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 59, "name": "I create an execution record with totaltimeinseconds 99999", "match": {"location": "automation/step-definitions/execution-record-steps.js:22"}, "result": {"status": "passed", "duration": 457499}}, {"arguments": [], "keyword": "Then ", "line": 60, "name": "the response status should be 201", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 56874, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +201\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy0rZkplOWJhdDkvdHd0dDJ0YTJWRis4N2FUQXdcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIyNDgxMTI5MgogIH0KfQ==", "mime_type": "application/json"}]}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 57}, {"name": "@Validation", "line": 57}], "type": "scenario"}, {"description": "", "id": "execution-record-api-operations;create-execution-record-with-boundary-values", "keyword": "Scenario Outline", "line": 67, "name": "Create execution record with boundary values", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 8791}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 714166}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtREpvNVNWU2RBclpmU3JLOVJSdlpSWFB3RDlVXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMjYyMDQxMjUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 59, "name": "I create an execution record with totaltimeinseconds -1", "match": {"location": "automation/step-definitions/execution-record-steps.js:22"}, "result": {"status": "passed", "duration": 690042}}, {"arguments": [], "keyword": "Then ", "line": 60, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 71999, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +400\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy1ESm81U1ZTZEFyWmZTcks5UlJ2WlJYUHdEOVVcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIyNjIwNDEyNQogIH0KfQ==", "mime_type": "application/json"}]}], "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 57}, {"name": "@Validation", "line": 57}], "type": "scenario"}], "id": "execution-record-api-operations", "line": 2, "keyword": "Feature", "name": "Execution Record API Operations", "tags": [{"name": "@ExecutionRecord", "line": 1}, {"name": "@Regression", "line": 1}], "uri": "automation/features/execution-record.feature"}, {"description": "  As a test automation engineer\n  I want to verify the API is healthy\n  So that I can run automation tests", "elements": [{"description": "", "id": "api-health-check;verify-api-health-check-endpoint", "keyword": "<PERSON><PERSON><PERSON>", "line": 11, "name": "Verify API health check endpoint", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 6374}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 714583}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtNU1kSEhpajhRbHpFOVQxdTV3NlJiMmEwdVpNXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMjgwNTMwODMKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "When ", "line": 12, "name": "I call the health check endpoint", "match": {"location": "automation/step-definitions/healthcheck-steps.js:4"}, "result": {"status": "failed", "duration": 82666, "error_message": "TypeError: this.apiHelper.get is not a function\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/healthcheck-steps.js:5:24)"}}, {"arguments": [], "keyword": "Then ", "line": 13, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 14, "name": "the response should contain health status", "match": {"location": "automation/step-definitions/healthcheck-steps.js:8"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@Regression", "line": 1}, {"name": "@Smoke", "line": 1}, {"name": "@Positive", "line": 10}, {"name": "@Functional", "line": 10}], "type": "scenario"}], "id": "api-health-check", "line": 2, "keyword": "Feature", "name": "API Health Check", "tags": [{"name": "@Regression", "line": 1}, {"name": "@Smoke", "line": 1}], "uri": "automation/features/healthcheck.feature"}, {"description": "  As a developer\n  I want to run a simple test\n  So that I can verify the setup works", "elements": [{"description": "", "id": "simple-test;basic-functionality-test", "keyword": "<PERSON><PERSON><PERSON>", "line": 7, "name": "Basic functionality test", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 5708}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "I have a simple test", "match": {"location": "automation/step-definitions/simple-steps.js:3"}, "result": {"status": "passed", "duration": 265290}}, {"arguments": [], "keyword": "When ", "line": 9, "name": "I run it", "match": {"location": "automation/step-definitions/simple-steps.js:7"}, "result": {"status": "passed", "duration": 70875}}, {"arguments": [], "keyword": "Then ", "line": 10, "name": "it should pass", "match": {"location": "automation/step-definitions/simple-steps.js:11"}, "result": {"status": "passed", "duration": 30459}}], "tags": [{"name": "@Regression", "line": 1}], "type": "scenario"}], "id": "simple-test", "line": 2, "keyword": "Feature", "name": "Simple Test", "tags": [{"name": "@Regression", "line": 1}], "uri": "automation/features/simple.feature"}, {"description": "  As a test automation engineer\n  I want to process images using Vertex AI\n  So that I can extract DOM elements and generate mappings", "elements": [{"description": "", "id": "vertex-ai-image-processing;process-image-with-valid-png-file-and-prompt", "keyword": "<PERSON><PERSON><PERSON>", "line": 12, "name": "Process image with valid PNG file and prompt", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 4124}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 668957}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtNUFUYjV0U1VEMXhPYWx2T0tNTElDSXhkcGNZXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMjk2MTkxNjcKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 9, "name": "I have a valid PNG image file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:6"}, "result": {"status": "passed", "duration": 97541}}, {"arguments": [], "keyword": "When ", "line": 13, "name": "I upload a PNG image with DOM object and prompt for element mapping", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:27"}, "result": {"status": "failed", "duration": 75540, "error_message": "TypeError: this.apiHelper.postWithFile is not a function\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/vertex-ai-steps.js:33:24)"}}, {"arguments": [], "keyword": "Then ", "line": 14, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 15, "name": "the response should contain AI generated content", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:101"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 16, "name": "the response should have valid structure", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:107"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@VertexAI", "line": 1}, {"name": "@AI", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 11}, {"name": "@Functional", "line": 11}], "type": "scenario"}, {"description": "", "id": "vertex-ai-image-processing;upload-non-png-image-file", "keyword": "<PERSON><PERSON><PERSON>", "line": 19, "name": "Upload non-PNG image file", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 6792}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 744874}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtczF6UEpZZkg4VmlkTEpEQWR4NFdIc1RqcUdFXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMzA5NDA5MTcKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 9, "name": "I have a valid PNG image file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:6"}, "result": {"status": "passed", "duration": 35249}}, {"arguments": [], "keyword": "When ", "line": 20, "name": "I upload a JPEG image file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:36"}, "result": {"status": "failed", "duration": 973583, "error_message": "TypeError: this.apiHelper.postWithFile is not a function\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/vertex-ai-steps.js:46:24)"}}, {"arguments": [], "keyword": "Then ", "line": 21, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 22, "name": "the response should contain error message about PNG requirement", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:112"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@VertexAI", "line": 1}, {"name": "@AI", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Negative", "line": 18}, {"name": "@FileValidation", "line": 18}], "type": "scenario"}, {"description": "", "id": "vertex-ai-image-processing;upload-request-without-image-file", "keyword": "<PERSON><PERSON><PERSON>", "line": 25, "name": "Upload request without image file", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 5458}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 827458}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtdlNjb1BnRWtlWlQwaUlnb1FxSlh5dXhTR0YwXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMzMwNDI4NzUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 9, "name": "I have a valid PNG image file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:6"}, "result": {"status": "passed", "duration": 27667}}, {"arguments": [], "keyword": "When ", "line": 26, "name": "I send vertex AI request without image file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:49"}, "result": {"status": "passed", "duration": 714042}}, {"arguments": [], "keyword": "Then ", "line": 27, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "failed", "duration": 96749, "error_message": "AssertionError\n    + expected - actual\n\n    -200\n    +400\n\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/common-steps.js:77:59)"}, "embeddings": [{"data": "TGFzdCBSZXF1ZXN0OiB7CiAgIm1ldGhvZCI6ICJHRVQiLAogICJ1cmwiOiAiaHR0cDovL2xvY2FsaG9zdDozMDAwL21pY3Jvc2VydmljZXMvYXV0b21hdGlvbi9hcGkvaGVhbHRoY2hlY2siLAogICJoZWFkZXJzIjoge30sCiAgImJvZHkiOiBudWxsCn0=", "mime_type": "application/json"}, {"data": "TGFzdCBSZXNwb25zZTogewogICJzdGF0dXMiOiAyMDAsCiAgInN0YXR1c1RleHQiOiAiT0siLAogICJoZWFkZXJzIjogewogICAgIngtcG93ZXJlZC1ieSI6ICJFeHByZXNzIiwKICAgICJhY2Nlc3MtY29udHJvbC1hbGxvdy1vcmlnaW4iOiAiKiIsCiAgICAiY29udGVudC10eXBlIjogImFwcGxpY2F0aW9uL2pzb247IGNoYXJzZXQ9dXRmLTgiLAogICAgImNvbnRlbnQtbGVuZ3RoIjogIjQ0IiwKICAgICJldGFnIjogIlcvXCIyYy12U2NvUGdFa2VaVDBpSWdvUXFKWHl1eFNHRjBcIiIsCiAgICAiZGF0ZSI6ICJUaHUsIDE3IEp1bCAyMDI1IDE4OjE4OjIwIEdNVCIsCiAgICAiY29ubmVjdGlvbiI6ICJrZWVwLWFsaXZlIiwKICAgICJrZWVwLWFsaXZlIjogInRpbWVvdXQ9NSIKICB9LAogICJkYXRhIjogewogICAgIm1lc3NhZ2UiOiAiSGVhbHRoeSIsCiAgICAidXB0aW1lIjogMzExLjIzMzA0Mjg3NQogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 28, "name": "the response should contain error message about missing file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:117"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@VertexAI", "line": 1}, {"name": "@AI", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Negative", "line": 24}, {"name": "@FileValidation", "line": 24}], "type": "scenario"}, {"description": "", "id": "vertex-ai-image-processing;upload-large-png-image-file", "keyword": "<PERSON><PERSON><PERSON>", "line": 31, "name": "Upload large PNG image file", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 10832}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 817457}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtemkwdENML2doYW9XeGNyOU1UZXk1MXZ1TlVBXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMzUyNzE4NzUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 9, "name": "I have a valid PNG image file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:6"}, "result": {"status": "passed", "duration": 23624}}, {"arguments": [], "keyword": "When ", "line": 32, "name": "I upload a PNG image larger than 10MB", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:57"}, "result": {"status": "failed", "duration": 2467459, "error_message": "TypeError: this.apiHelper.postWithFile is not a function\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/vertex-ai-steps.js:68:24)"}}, {"arguments": [], "keyword": "Then ", "line": 33, "name": "the response status should be 413", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 34, "name": "the response should contain file size error message", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:122"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@VertexAI", "line": 1}, {"name": "@AI", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Boundary", "line": 30}, {"name": "@FileSize", "line": 30}], "type": "scenario"}, {"description": "", "id": "vertex-ai-image-processing;process-image-with-complex-dom-object", "keyword": "<PERSON><PERSON><PERSON>", "line": 37, "name": "Process image with complex DOM object", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 9749}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 870249}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0NCIsCiAgICAiZXRhZyI6ICJXL1wiMmMtU3dQUmNrVXptWmdlVlFrajFOM2pPM2c2ODc0XCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yMzkwNTc2MjUKICB9Cn0=", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 9, "name": "I have a valid PNG image file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:6"}, "result": {"status": "passed", "duration": 39166}}, {"arguments": [], "keyword": "When ", "line": 38, "name": "I upload a PNG image with complex DOM structure", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:71"}, "result": {"status": "failed", "duration": 93042, "error_message": "TypeError: this.apiHelper.postWithFile is not a function\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/vertex-ai-steps.js:89:24)"}}, {"arguments": [], "keyword": "Then ", "line": 39, "name": "the response status should be 200", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 40, "name": "the AI response should contain table format mapping", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:127"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 41, "name": "the response should include button names and input names", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:132"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@VertexAI", "line": 1}, {"name": "@AI", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Positive", "line": 36}, {"name": "@Functional", "line": 36}], "type": "scenario"}, {"description": "", "id": "vertex-ai-image-processing;process-image-with-empty-prompt", "keyword": "<PERSON><PERSON><PERSON>", "line": 44, "name": "Process image with empty prompt", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 5291}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "the automation API is available", "match": {"location": "automation/step-definitions/common-steps.js:14"}, "result": {"status": "passed", "duration": 763292}, "embeddings": [{"data": "4pyFIEFQSSBTZXJ2ZXIgQXZhaWxhYmxlIGF0OiBodHRwOi8vbG9jYWxob3N0OjMwMDAvbWljcm9zZXJ2aWNlcy9hdXRvbWF0aW9uL2FwaS9oZWFsdGhjaGVjaw==", "mime_type": "text/plain"}, {"data": "UmVxdWVzdDogewogICJtZXRob2QiOiAiR0VUIiwKICAidXJsIjogImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9taWNyb3NlcnZpY2VzL2F1dG9tYXRpb24vYXBpL2hlYWx0aGNoZWNrIiwKICAiaGVhZGVycyI6IHt9LAogICJib2R5IjogbnVsbAp9", "mime_type": "application/json"}, {"data": "UmVzcG9uc2U6IHsKICAic3RhdHVzIjogMjAwLAogICJzdGF0dXNUZXh0IjogIk9LIiwKICAiaGVhZGVycyI6IHsKICAgICJ4LXBvd2VyZWQtYnkiOiAiRXhwcmVzcyIsCiAgICAiYWNjZXNzLWNvbnRyb2wtYWxsb3ctb3JpZ2luIjogIioiLAogICAgImNvbnRlbnQtdHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04IiwKICAgICJjb250ZW50LWxlbmd0aCI6ICI0MyIsCiAgICAiZXRhZyI6ICJXL1wiMmItaHp5M1Y3czBXZWdReUh1bWlqWGxIcGM5bDBzXCIiLAogICAgImRhdGUiOiAiVGh1LCAxNyBKdWwgMjAyNSAxODoxODoyMCBHTVQiLAogICAgImNvbm5lY3Rpb24iOiAia2VlcC1hbGl2ZSIsCiAgICAia2VlcC1hbGl2ZSI6ICJ0aW1lb3V0PTUiCiAgfSwKICAiZGF0YSI6IHsKICAgICJtZXNzYWdlIjogIkhlYWx0aHkiLAogICAgInVwdGltZSI6IDMxMS4yNDAzODY3NQogIH0KfQ==", "mime_type": "application/json"}]}, {"arguments": [], "keyword": "And ", "line": 9, "name": "I have a valid PNG image file", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:6"}, "result": {"status": "passed", "duration": 22541}}, {"arguments": [], "keyword": "When ", "line": 45, "name": "I upload a PNG image with empty prompt", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:92"}, "result": {"status": "failed", "duration": 157041, "error_message": "TypeError: this.apiHelper.postWithFile is not a function\n    at World.<anonymous> (/Users/<USER>/Desktop/Automation_Enterprise/New/API_POC/1/dhs-health-solutions-automation-api/automation/step-definitions/vertex-ai-steps.js:98:24)"}}, {"arguments": [], "keyword": "Then ", "line": 46, "name": "the response status should be 400", "match": {"location": "automation/step-definitions/common-steps.js:68"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 47, "name": "the response should contain validation error for prompt", "match": {"location": "automation/step-definitions/vertex-ai-steps.js:138"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@VertexAI", "line": 1}, {"name": "@AI", "line": 1}, {"name": "@Regression", "line": 1}, {"name": "@Negative", "line": 43}, {"name": "@Validation", "line": 43}], "type": "scenario"}], "id": "vertex-ai-image-processing", "line": 2, "keyword": "Feature", "name": "Vertex AI Image Processing", "tags": [{"name": "@VertexAI", "line": 1}, {"name": "@AI", "line": 1}, {"name": "@Regression", "line": 1}], "uri": "automation/features/vertex-ai.feature"}]