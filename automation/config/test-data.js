module.exports = {
  baseUrl: process.env.API_BASE_URL || 'http://localhost:3000/microservices/automation',
  
  executionRecord: {
    valid: {
      testenv: "prod",
      cc_pipelineid: "15683029531",
      cc_jobid: "2401",
      cc_jobname: "@AI_MCCore_Dailyrun_EntryPoint_One",
      workflowname: "MC Core and VM Automation Trigger Pipeline",
      parenttag: "@AI_MCCore_Dailyrun_EntryPoint_One",
      scenariooutline: "Validate Group NGS Entry points CVS Homepage Covid-19 Footer link #1",
      platform: "Web",
      status: "PASSED",
      failedstep: "",
      failedreason: "",
      qmsessiondetails: "",
      qmsessionlink: "undefined",
      failedcategory: "",
      execution_timestamp: "2025-01-06T14:11:41.000Z",
      timetaken: "0 minutes :16 seconds",
      totaltimeinseconds: 16,
      triggeredby: "Scheduled Run",
      artifactsurl: "https://github.com/cvs-health-source-code/dhs-health-solutions-ui-automation/actions/runs/15683029531",
      jiraissueid: "",
      defectid: "123"
    }
  },

  apiExecutionRecord: {
    valid: {
      apiname: "test3",
      status: "PASSED",
      testexecutiondateandtime: "2025-04-17 11:07:23",
      statuscode: null,
      statusdesc: null,
      response: null,
      response_headers: null,
      responsetime: null,
      jiraissueid: "A123",
      defectid: "1234"
    }
  }
};
