const { Given, When, Then } = require('@cucumber/cucumber');
const { expect } = require('chai');
const testData = require('../config/test-data');

When('I create an appointment record with valid data', async function () {
  const payload = { data: testData.apptRecord.valid };
  await this.apiHelper.post('/CreateApptRecord', payload);
});

When('I create an appointment record with empty confirmationcode', async function () {
  const invalidData = { ...testData.apptRecord.valid, confirmationcode: '' };
  const payload = { data: invalidData };
  await this.apiHelper.post('/CreateApptRecord', payload);
});

Given('I have created an appointment record', async function () {
  const payload = { data: testData.apptRecord.valid };
  const response = await this.apiHelper.post('/CreateApptRecord', payload);
  this.createdApptRecord = response.data;
});

Given('I have created an appointment record with confirmationcode', async function () {
  const payload = { data: testData.apptRecord.valid };
  const response = await this.apiHelper.post('/CreateApptRecord', payload);
  this.createdApptConfirmationCode = response.data.data.confirmationcode;
});

Given('I have created an appointment record with date {string}', async function (date) {
  const data = { ...testData.apptRecord.valid, apptcreationdate: date };
  const payload = { data };
  const response = await this.apiHelper.post('/CreateApptRecord', payload);
  this.createdApptRecord = response.data;
});

When('I search appointments by lob {string} and clinictype {string}', async function (lob, clinictype) {
  const payload = {
    data: {},
    lobmatch: lob,
    clinictype: clinictype
  };
  await this.apiHelper.post('/ApptRecordByData', payload);
});

When('I search appointments with date {string} and comparison {string}', async function (date, comparison) {
  const payload = {
    data: {},
    apptcreationdate: date,
    comparison: comparison
  };
  await this.apiHelper.post('/ApptRecordByData', payload);
});

When('I update the appointment record with new cancel_status {string}', async function (cancelStatus) {
  const updateData = { data: { cancel_status: cancelStatus } };
  await this.apiHelper.put(`/UpdateApptRecord?confirmationcode=${this.createdApptConfirmationCode}`, updateData);
});

Then('the response should contain appointment record data', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.have.property('data');
  expect(response.data.data).to.be.an('object');
});

Then('the appointment should have correct confirmationcode', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.confirmationcode).to.equal(testData.apptRecord.valid.confirmationcode);
});

Then('the appointment should have correct lob {string}', function (expectedLob) {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.lob).to.equal(expectedLob);
});

Then('the response should contain matching appointment records', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.be.an('array');
  expect(response.data.length).to.be.greaterThan(0);
});

Then('the updated appointment should have cancel_status {string}', function (expectedStatus) {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.cancel_status).to.equal(expectedStatus);
});