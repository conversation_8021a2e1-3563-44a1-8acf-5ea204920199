const { Given, When, Then } = require('@cucumber/cucumber');
const axios = require('axios');
const { expect } = require('chai');
const testData = require('../config/test-data');

When('I create an API execution record with valid data', async function () {
  const baseUrl = this.baseUrl || 'http://localhost:3000';
  const url = `${baseUrl}/microservices/automation/CreateAPIExecutionRecord`;
  
  const requestData = {
    data: {
      apiname: "test3",
      status: "PASSED",
      testexecutiondateandtime: "2025-04-17 11:07:23",
      statuscode: null,
      statusdesc: null,
      response: null,
      response_headers: null,
      responsetime: null,
      jiraissueid: "A123",
      defectid: "1234"
    }
  };

  try {
    const requestInfo = {
      method: 'POST',
      url: url,
      headers: {
        'Content-Type': 'application/json'
      },
      body: requestData
    };
    
    this.attach(`📤 REQUEST:\n${JSON.stringify(requestInfo, null, 2)}`, 'application/json');
    
    const response = await axios.post(url, requestData, {
      headers: { 'Content-Type': 'application/json' },
      validateStatus: () => true
    });
    
    const responseInfo = {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data,
      duration: response.headers['x-response-time'] || 'N/A'
    };
    
    this.attach(`📥 RESPONSE:\n${JSON.stringify(responseInfo, null, 2)}`, 'application/json');
    
    this.response = response;
    
  } catch (error) {
    const errorInfo = {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers
    };
    
    this.attach(`❌ ERROR:\n${JSON.stringify(errorInfo, null, 2)}`, 'application/json');
    this.response = error.response;
  }
});

When('I create an API execution record with empty apiname', async function () {
  const invalidData = { ...testData.apiExecutionRecord.valid, apiname: '' };
  const payload = { data: invalidData };
  await this.apiHelper.post('/CreateAPIExecutionRecord', payload);
});

When('I retrieve all API execution records', async function () {
  const baseUrl = this.baseUrl || 'http://localhost:3000';
  const url = `${baseUrl}/microservices/automation/api-execution-records`;
  
  try {
    const requestInfo = {
      method: 'GET',
      url: url,
      headers: {}
    };
    
    this.attach(`📤 REQUEST:\n${JSON.stringify(requestInfo, null, 2)}`, 'application/json');
    
    const response = await axios.get(url, {
      validateStatus: () => true
    });
    
    const responseInfo = {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data,
      recordCount: Array.isArray(response.data) ? response.data.length : 'N/A'
    };
    
    this.attach(`📥 RESPONSE:\n${JSON.stringify(responseInfo, null, 2)}`, 'application/json');
    
    this.response = response;
    
  } catch (error) {
    const errorInfo = {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    };
    
    this.attach(`❌ ERROR:\n${JSON.stringify(errorInfo, null, 2)}`, 'application/json');
    this.response = error.response;
  }
});

When('I update the API execution record with new status {string}', async function (status) {
  const updateData = { data: { status } };
  await this.apiHelper.put(`/UpdateAPIExecutionRecord?sno=${this.createdAPIRecordSno}`, updateData);
});

Then('the response should contain API execution record data', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.have.property('data');
  expect(response.data.data).to.be.an('object');
});

Then('the API record should have correct apiname {string}', function (expectedApiname) {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.apiname).to.equal(expectedApiname);
});

Then('the API record should have correct status {string}', function (expectedStatus) {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.status).to.equal(expectedStatus);
});

Then('the response should contain array of API execution records', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.be.an('array');
});

Then('the updated API record should have status {string}', function (expectedStatus) {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.status).to.equal(expectedStatus);
});
