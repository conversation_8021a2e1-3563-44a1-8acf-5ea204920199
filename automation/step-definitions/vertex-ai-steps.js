const { Given, When, Then } = require('@cucumber/cucumber');
const { expect } = require('chai');
const path = require('path');
const fs = require('fs');

Given('I have a valid PNG image file', function () {
  this.validImagePath = path.join(__dirname, '../test-files/sample.png');
  // Create a sample PNG file if it doesn't exist
  if (!fs.existsSync(this.validImagePath)) {
    fs.mkdirSync(path.dirname(this.validImagePath), { recursive: true });
    // Create a minimal PNG file (1x1 pixel)
    const pngBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
      0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
      0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,
      0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41,
      0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42,
      0x60, 0x82
    ]);
    fs.writeFileSync(this.validImagePath, pngBuffer);
  }
});

When('I upload a PNG image with DOM object and prompt for element mapping', async function () {
  const formData = {
    domObject: '<formly-field-text-input><ps-input><ps-label><label>First name</label></ps-label><input id="patientInfoFirstName" type="text"></ps-input></formly-field-text-input>',
    prompt: 'Put into a table all button names, label names, input names from the screenshot below and map xpaths from the DOM content below.'
  };
  
  await this.apiHelper.postWithFile('/vertexai', this.validImagePath, formData);
});

When('I upload a JPEG image file', async function () {
  // Create a mock JPEG file
  const jpegPath = path.join(__dirname, '../test-files/sample.jpg');
  fs.writeFileSync(jpegPath, Buffer.from('fake jpeg content'));
  
  const formData = {
    domObject: '<div>test</div>',
    prompt: 'test prompt'
  };
  
  await this.apiHelper.postWithFile('/vertexai', jpegPath, formData);
});

When('I send vertex AI request without image file', async function () {
  const payload = {
    domObject: '<div>test</div>',
    prompt: 'test prompt'
  };
  await this.apiHelper.post('/vertexai', payload);
});

When('I upload a PNG image larger than 10MB', async function () {
  // Create a large file (mock)
  const largePath = path.join(__dirname, '../test-files/large.png');
  const largeBuffer = Buffer.alloc(11 * 1024 * 1024); // 11MB
  fs.writeFileSync(largePath, largeBuffer);
  
  const formData = {
    domObject: '<div>test</div>',
    prompt: 'test prompt'
  };
  
  await this.apiHelper.postWithFile('/vertexai', largePath, formData);
});

When('I upload a PNG image with complex DOM structure', async function () {
  const complexDOM = `<formly-field-text-input _nghost-ng-c3535419138="">
    <ps-input _ngcontent-ng-c3535419138="" input-type="text" class="ng-untouched ng-pristine ng-invalid ps-input hydrated ps-input-error">
      <ps-label class="hydrated">
        <label class="ps-label ps-label-is-bold" for="patientInfoFirstName">First name</label>
      </ps-label>
      <input _ngcontent-ng-c3535419138="" slot="input" required="" autocomplete="given-name" inputmode="text" id="patientInfoFirstName" type="text" class="" aria-describedby="patientInfoFirstName-error">
      <ps-error is-error-announced="false" class="hydrated">
        <div class="ps-error" id="patientInfoFirstName-error">Enter a first name</div>
      </ps-error>
    </ps-input>
  </formly-field-text-input>`;
  
  const formData = {
    domObject: complexDOM,
    prompt: 'Put into a table all button names, label names, input names from the screenshot below and map xpaths from the DOM content below. Do not add any text only provide a mapping table.'
  };
  
  await this.apiHelper.postWithFile('/vertexai', this.validImagePath, formData);
});

When('I upload a PNG image with empty prompt', async function () {
  const formData = {
    domObject: '<div>test</div>',
    prompt: ''
  };
  
  await this.apiHelper.postWithFile('/vertexai', this.validImagePath, formData);
});

Then('the response should contain AI generated content', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.have.property('text');
  expect(response.data.text).to.be.a('string');
});

Then('the response should have valid structure', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.be.an('object');
});

Then('the response should contain error message about PNG requirement', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.message).to.include('PNG');
});

Then('the response should contain error message about missing file', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.message).to.include('file');
});

Then('the response should contain file size error message', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.message).to.include('size');
});

Then('the AI response should contain table format mapping', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.text).to.include('table');
});

Then('the response should include button names and input names', function () {
  const response = this.apiHelper.getLastResponse();
  const text = response.data.text.toLowerCase();
  expect(text).to.satisfy(text => text.includes('button') || text.includes('input'));
});

Then('the response should contain validation error for prompt', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.message).to.include('prompt');
});