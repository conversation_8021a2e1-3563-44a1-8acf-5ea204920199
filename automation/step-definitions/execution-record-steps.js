const { Given, When, Then } = require('@cucumber/cucumber');
const { expect } = require('chai');
const testData = require('../config/test-data');

When('I create an execution record with valid data', async function () {
  const payload = { data: testData.executionRecord.valid };
  await this.apiHelper.post('/CreateExecutionRecord', payload);
});

When('I create an execution record with empty parenttag', async function () {
  const invalidData = { ...testData.executionRecord.valid, parenttag: '' };
  const payload = { data: invalidData };
  await this.apiHelper.post('/CreateExecutionRecord', payload);
});

When('I create an execution record with invalid status {string}', async function (status) {
  const invalidData = { ...testData.executionRecord.valid, status };
  const payload = { data: invalidData };
  await this.apiHelper.post('/CreateExecutionRecord', payload);
});

When('I create an execution record with totaltimeinseconds {int}', async function (value) {
  const data = { ...testData.executionRecord.valid, totaltimeinseconds: value };
  const payload = { data };
  await this.apiHelper.post('/CreateExecutionRecord', payload);
});

Given('I have created an execution record', async function () {
  const payload = { data: testData.executionRecord.valid };
  const response = await this.apiHelper.post('/CreateExecutionRecord', payload);
  this.createdRecord = response.data;
});

Given('I have created an execution record with id', async function () {
  const payload = { data: testData.executionRecord.valid };
  const response = await this.apiHelper.post('/CreateExecutionRecord', payload);
  this.createdRecordId = response.data.data.id;
});

When('I retrieve all execution records', async function () {
  await this.apiHelper.get('/ExecutionData');
});

When('I search execution records by parenttag {string}', async function (parenttag) {
  const payload = { data: { parenttag } };
  await this.apiHelper.post('/ExecutionRecordByData', payload);
});

When('I update the execution record with new status {string}', async function (status) {
  const updateData = { data: { status } };
  await this.apiHelper.put(`/UpdateExecutionRecord?id=${this.createdRecordId}`, updateData);
});

Then('the response should contain execution record data', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.have.property('data');
  expect(response.data.data).to.be.an('object');
});

Then('the execution record should have correct parenttag', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.parenttag).to.equal(testData.executionRecord.valid.parenttag);
});

Then('the execution record should have correct status', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.status).to.equal(testData.executionRecord.valid.status);
});

Then('the response should contain array of execution records', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.be.an('array');
});

Then('the response should contain matching execution records', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.be.an('array');
  expect(response.data.length).to.be.greaterThan(0);
});

Then('the updated record should have status {string}', function (expectedStatus) {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.data.status).to.equal(expectedStatus);
});

Then('the response should contain validation error for status field', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data.message).to.include('status');
});