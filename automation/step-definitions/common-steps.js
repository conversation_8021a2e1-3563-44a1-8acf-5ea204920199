const { Given, When, Then, Before } = require('@cucumber/cucumber');
const axios = require('axios');
const { expect } = require('chai');
const ApiHelper = require('../support/api-helper');

let lastRequest = {};
let lastResponse = {};

Before(function() {
  lastRequest = {};
  lastResponse = {};
});

Given('the automation API is available', async function () {
  const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';
  
  // Try different possible health endpoints
  const healthEndpoints = [
    `${baseUrl}/microservices/automation/api/healthcheck`,
    `${baseUrl}/microservices/automation/healthcheck`,
    `${baseUrl}/microservices/automation/health`,
    `${baseUrl}/health`
  ];
  
  let response;
  let workingEndpoint;
  
  for (const endpoint of healthEndpoints) {
    try {
      response = await axios.get(endpoint, { timeout: 5000 });
      workingEndpoint = endpoint;
      break;
    } catch (error) {
      continue;
    }
  }
  
  if (!response) {
    const errorMessage = `❌ API Server is not running at ${baseUrl}. Tried endpoints: ${healthEndpoints.join(', ')}`;
    this.attach(errorMessage, 'text/plain');
    throw new Error(errorMessage);
  }
  
  this.baseUrl = baseUrl;
  
  // Initialize API Helper
  this.apiHelper = new ApiHelper(`${baseUrl}/microservices/automation`);
  
  lastRequest = {
    method: 'GET',
    url: workingEndpoint,
    headers: {},
    body: null
  };
  
  lastResponse = {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers,
    data: response.data
  };
  
  this.attach(`✅ API Server Available at: ${workingEndpoint}`, 'text/plain');
  this.attach(`Request: ${JSON.stringify(lastRequest, null, 2)}`, 'application/json');
  this.attach(`Response: ${JSON.stringify(lastResponse, null, 2)}`, 'application/json');
});

Then('the response status should be {int}', function (expectedStatus) {
  if (Object.keys(lastRequest).length > 0) {
    this.attach(`Last Request: ${JSON.stringify(lastRequest, null, 2)}`, 'application/json');
  }
  
  if (Object.keys(lastResponse).length > 0) {
    this.attach(`Last Response: ${JSON.stringify(lastResponse, null, 2)}`, 'application/json');
  }
  
  expect(this.response?.status || lastResponse.status).to.equal(expectedStatus);
});

Then('the response should contain validation error message', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.have.property('message');
  expect(response.data.message).to.be.a('string');
});

Then('the response should contain not found error message', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.have.property('message');
  expect(response.data.message).to.include('not found');
});

// Export for use in other step files
module.exports = { lastRequest, lastResponse };
