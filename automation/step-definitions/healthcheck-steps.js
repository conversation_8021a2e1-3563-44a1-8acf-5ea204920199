const { Given, When, Then } = require('@cucumber/cucumber');
const { expect } = require('chai');

When('I call the health check endpoint', async function () {
  await this.apiHelper.get('/healthcheck');
});

Then('the response should contain health status', function () {
  const response = this.apiHelper.getLastResponse();
  expect(response.data).to.have.property('message');
  expect(response.data.message).to.equal('Healthy');
});