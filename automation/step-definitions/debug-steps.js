const { Given, When, Then } = require('@cucumber/cucumber');
const axios = require('axios');

When('I debug available endpoints', async function () {
  const baseUrl = this.baseUrl || 'http://localhost:3000';
  
  // Try common endpoint patterns
  const endpointsToTry = [
    '/microservices/automation/CreateAPIExecutionRecord',
    '/microservices/automation/api/CreateAPIExecutionRecord', 
    '/microservices/automation/api-execution-records',
    '/microservices/automation/api/api-execution-records',
    '/microservices/automation/APIExecutionData',
    '/microservices/automation/api/APIExecutionData',
    '/microservices/automation/execution-records',
    '/microservices/automation/api/execution-records'
  ];
  
  console.log('\n=== DEBUGGING ENDPOINTS ===');
  
  for (const endpoint of endpointsToTry) {
    const fullUrl = `${baseUrl}${endpoint}`;
    
    try {
      // Try GET first
      const getResponse = await axios.get(fullUrl, { 
        timeout: 2000,
        validateStatus: () => true 
      });
      console.log(`✅ GET ${endpoint} -> ${getResponse.status}`);
      
      // Try POST
      const postResponse = await axios.post(fullUrl, {}, { 
        timeout: 2000,
        validateStatus: () => true 
      });
      console.log(`✅ POST ${endpoint} -> ${postResponse.status}`);
      
    } catch (error) {
      console.log(`❌ ${endpoint} -> ${error.code || error.message}`);
    }
  }
  
  console.log('=== END DEBUG ===\n');
});