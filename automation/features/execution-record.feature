@ExecutionRecord @Regression
Feature: Execution Record API Operations
  As a test automation engineer
  I want to perform CRUD operations on execution records
  So that I can manage test execution data effectively

  Background:
    Given the automation API is available

  @Positive @Functional
  Scenario: Create execution record with valid data
    When I create an execution record with valid data
    Then the response status should be 201
    And the response should contain execution record data
    And the execution record should have correct parenttag
    And the execution record should have correct status

  @Negative @Validation
  Scenario: Create execution record with missing required fields
    When I create an execution record with empty parenttag
    Then the response status should be 400
    And the response should contain validation error message

  @Negative @Validation
  Scenario: Create execution record with invalid status
    When I create an execution record with invalid status "INVALID_STATUS"
    Then the response status should be 400
    And the response should contain validation error for status field

  @Positive @Functional
  Scenario: Retrieve all execution records
    Given I have created an execution record
    When I retrieve all execution records
    Then the response status should be 200
    And the response should contain array of execution records

  @Positive @Functional
  Scenario: Retrieve execution records by criteria
    Given I have created an execution record
    When I search execution records by parenttag "@AI_MCCore_Dailyrun_EntryPoint_One"
    Then the response status should be 200
    And the response should contain matching execution records

  @Negative @NotFound
  Scenario: Retrieve execution records with non-existent criteria
    When I search execution records by parenttag "NON_EXISTENT_TAG"
    Then the response status should be 404
    And the response should contain not found error message

  @Positive @Functional
  Scenario: Update execution record with valid data
    Given I have created an execution record with id
    When I update the execution record with new status "FAILED"
    Then the response status should be 200
    And the updated record should have status "FAILED"

  @Boundary @Validation
  Scenario Outline: Create execution record with boundary values
    When I create an execution record with totaltimeinseconds <value>
    Then the response status should be <expectedStatus>

    Examples:
      | value | expectedStatus |
      | 0     | 201           |
      | 1     | 201           |
      | 99999 | 201           |
      | -1    | 400           |