@APIExecutionRecord @Regression
Feature: API Execution Record Operations
  As a test automation engineer
  I want to manage API execution records
  So that I can track API test results

  Background:
    Given the automation API is available

  @Positive @Functional
  Scenario: Create API execution record with valid data
    When I create an API execution record with valid data
    Then the response status should be 201
    And the response should contain API execution record data
    And the API record should have correct apiname "test3"
    And the API record should have correct status "PASSED"

  @Negative @Validation
  Scenario: Create API execution record with missing required fields
    When I create an API execution record with empty apiname
    Then the response status should be 400
    And the response should contain validation error message

  @Positive @Functional
  Scenario: Retrieve all API execution records
    Given I have created an API execution record
    When I retrieve all API execution records
    Then the response status should be 200
    And the response should contain array of API execution records

  @Positive @Functional
  Scenario: Update API execution record
    Given I have created an API execution record with sno
    When I update the API execution record with new status "FAILED"
    Then the response status should be 200
    And the updated API record should have status "FAILED"

  @Boundary @DateTime
  Scenario Outline: Create API execution record with various date formats
    When I create an API execution record with testexecutiondateandtime "<dateTime>"
    Then the response status should be <expectedStatus>

    Examples:
      | dateTime              | expectedStatus |
      | 2025-04-17 11:07:23  | 201           |
      | 2025-12-31 23:59:59  | 201           |
      | invalid_date         | 400           |
      | 2025-13-01 11:07:23  | 400           |