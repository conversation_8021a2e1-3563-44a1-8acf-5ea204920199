@VertexAI @AI @Regression
Feature: Vertex AI Image Processing
  As a test automation engineer
  I want to process images using Vertex AI
  So that I can extract DOM elements and generate mappings

  Background:
    Given the automation API is available
    And I have a valid PNG image file

  @Positive @Functional
  Scenario: Process image with valid PNG file and prompt
    When I upload a PNG image with DOM object and prompt for element mapping
    Then the response status should be 200
    And the response should contain AI generated content
    And the response should have valid structure

  @Negative @FileValidation
  Scenario: Upload non-PNG image file
    When I upload a JPEG image file
    Then the response status should be 400
    And the response should contain error message about PNG requirement

  @Negative @FileValidation
  Scenario: Upload request without image file
    When I send vertex AI request without image file
    Then the response status should be 400
    And the response should contain error message about missing file

  @Boundary @FileSize
  Scenario: Upload large PNG image file
    When I upload a PNG image larger than 10MB
    Then the response status should be 413
    And the response should contain file size error message

  @Positive @Functional
  Scenario: Process image with complex DOM object
    When I upload a PNG image with complex DOM structure
    Then the response status should be 200
    And the AI response should contain table format mapping
    And the response should include button names and input names

  @Negative @Validation
  Scenario: Process image with empty prompt
    When I upload a PNG image with empty prompt
    Then the response status should be 400
    And the response should contain validation error for prompt