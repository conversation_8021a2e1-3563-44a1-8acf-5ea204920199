@AppointmentR<PERSON>ord @Regression
Feature: Appointment Record Operations
  As a test automation engineer
  I want to manage appointment records
  So that I can track appointment booking data

  Background:
    Given the automation API is available

  @Positive @Functional
  Scenario: Create appointment record with valid data
    When I create an appointment record with valid data
    Then the response status should be 201
    And the response should contain appointment record data
    And the appointment should have correct confirmationcode
    And the appointment should have correct lob "mc"

  @Negative @Validation
  Scenario: Create appointment record with missing confirmation code
    When I create an appointment record with empty confirmationcode
    Then the response status should be 400
    And the response should contain validation error message

  @Positive @Functional
  Scenario: Retrieve appointments by LOB and clinic type
    Given I have created an appointment record
    When I search appointments by lob "mc" and clinictype "minuteclinic"
    Then the response status should be 200
    And the response should contain matching appointment records

  @Positive @Functional
  Scenario: Update appointment record
    Given I have created an appointment record with confirmationcode
    When I update the appointment record with new cancel_status "Canceled"
    Then the response status should be 200
    And the updated appointment should have cancel_status "Canceled"

  @Boundary @DateComparison
  Scenario Outline: Search appointments with date comparisons
    Given I have created an appointment record with date "2025-06-26 07:17:46"
    When I search appointments with date "2025-06-25 07:17:46" and comparison "<comparison>"
    Then the response status should be <expectedStatus>

    Examples:
      | comparison | expectedStatus |
      | gt         | 200           |
      | lt         | 200           |
      | eq         | 200           |