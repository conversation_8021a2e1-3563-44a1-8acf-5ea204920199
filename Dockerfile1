FROM node:20-alpine AS base
WORKDIR /home/<USER>
COPY package*.json ./
RUN npm install
COPY dist/. /home/<USER>/health-solutions-automation-api
WORKDIR /home/<USER>/health-solutions-automation-api
RUN npm install pg@8.12.0
FROM cvsh.jfrog.io/cvsdigital-docker-local/devsecops/distroless/node-20-distroless:latest
WORKDIR /app
USER 9000:9000
COPY --from=base /home/<USER>/health-solutions-automation-api ./
EXPOSE 3000
CMD ["node", "main.js"]

# FROM node:20-alpine AS base
# WORKDIR /home/<USER>
# COPY package*.json ./
# COPY dist/. /home/<USER>/health-solutions-automation-api
# RUN echo $(ls)
# WORKDIR /home/<USER>/health-solutions-automation-api
# RUN npm install && npm install pg@8.12.0
# FROM cvsh.jfrog.io/cvsdigital-docker-local/devsecops/distroless/node-20-distroless:latest
# WORKDIR /app
# USER 9000:9000
# COPY --from=base /home/<USER>/health-solutions-automation-api ./
# EXPOSE 3000
# CMD ["node", "main.js"]