
  name: Run CICD Pipeline

  on:
    pull_request:
      branches:
        - main

  jobs:
    ci-pr:
      uses: cvs-health-source-code/gha_workflow_actions/.github/workflows/npm_docker.yaml@latest
      with:
        NODE_VERSION: "20.18.0"
        ARTIFACT_NAME: "automation-vertexai-api"
        SCA_TOOL: 'snyk'
        SAST_TOOL: 'snyk'
        CONTAINER_SCAN_TOOL: 'snyk'
        NPM_DEPS_COMMAND: "npm ci"
        NPM_BUILD_COMMAND: "bash scripts/build.sh"
        NPM_TEST_COMMAND: "bash scripts/test.sh"
        NPM_LINT_COMMAND: "bash scripts/lint.sh"
      secrets: inherit