{"name": "dhs-health-solutions-automation-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nodemon", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "db:drop": "pnpm run typeorm schema:drop -d src/ormconfig.ts", "db:executiondata": "pnpm run typeorm migration:generate -d src/ormconfig.ts src/migrations/executionData", "db:migrate": "pnpm run typeorm migration:run -d src/ormconfig.ts", "test:api": "bash automation/scripts/run-tests.sh", "test:api:dev": "npx cucumber-js automation/features --require automation/step-definitions --format json:automation/reports/cucumber-report.json --format html:automation/reports/cucumber-report.html", "setup": "node -e \"console.log('Setup starting...'); require('fs').mkdirSync('automation/reports', {recursive: true}); console.log('Setup complete');\"", "test:simple": "npx cucumber-js automation/features/simple.feature --require automation/step-definitions"}, "dependencies": {"@google-cloud/vertexai": "^1.10.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.1.2", "@nestjs/typeorm": "^11.0.0", "@types/dotenv": "^8.2.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "module-alias": "^2.2.3", "nodemon": "^3.1.10", "pg": "^8.15.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.23"}, "devDependencies": {"@cucumber/cucumber": "^10.9.0", "@cucumber/html-formatter": "^21.3.1", "@cucumber/pretty-formatter": "^1.0.1", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "axios": "^1.10.0", "chai": "^4.5.0", "cucumber-html-reporter": "^7.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "form-data": "^4.0.4", "fs-extra": "^11.2.0", "globals": "^16.0.0", "jest": "^29.7.0", "multiple-cucumber-html-reporter": "^3.9.3", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "_moduleAliases": {"@app": "./dist", "@test": "./test"}}