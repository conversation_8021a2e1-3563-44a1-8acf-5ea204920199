import { Body, Controller, Put, UsePipes, ValidationPipe, UploadedFile, UseInterceptors, BadRequestException } from "@nestjs/common";
import {FileInterceptor} from '@nestjs/platform-express';
import { Get, Res, Post, Delete } from "@nestjs/common";
import { Response } from "express";
import { AutomationService } from './automation.service';
import { AutomationEntity, APIEntity, ApptEntity, CucumberTagsEntity } from './automation.entity';
import { CreateExecutionRecordDto, CreateAPIExecutionRecordDto, CreateApptRecordDto,CreateCucumberTagsDto } from "./dto/createExecutionRecord.dto";
import { UpdateExecutionRecordDto , UpdateAPIExecutionRecordDto, UpdateApptRecordDto, UpdateCucumberTagsDto } from "./dto/updateExecutionRecord.dto";
import { Record } from "./decorators/record.decorator";
import { VertexAiRequestDto } from "./dto/vertex-ai.dto";

// Add this type declaration
declare global {
  namespace Express {
    interface Multer {
      File: {
        fieldname: string;
        originalname: string;
        encoding: string;
        mimetype: string;
        size: number;
        destination: string;
        filename: string;
        path: string;
        buffer: Buffer;
      }
    }
  }
}

@Controller('api')
export class AutomationController {
  constructor(private readonly automationService: AutomationService) {}

  @Get('healthcheck')
  healthCheck(@Res() res: Response) {
    const uptime = process.uptime();
    return res.status(200).json({ message: 'Healthy', uptime });
  }

  @Get('ExecutionData')
  async findAll(): Promise<AutomationEntity[]> {
    console.log('inside automation controller');
    return await this.automationService.findAll();
  }

  @Get('CucumberTagsData')
  async findCucumberTagsData(): Promise<CucumberTagsEntity[]> {
    console.log('inside automation controller');
    return await this.automationService.findCucumberTagsData();
  }

  @Post('ExecutionRecordByData')
  async findExecutionRecordByData(
    @Body('data') data: Partial<AutomationEntity>
  ): Promise<AutomationEntity[]> {
    console.log('inside automation controller', data);
    return await this.automationService.findExecutionRecordByData(data);
  }

  @Get('APIExecutionData')
  async findAPIExecutionData(): Promise<APIEntity[]> {
    console.log('inside automation controller');
    return await this.automationService.findAPIExecutionData();
  }

  @Post('APIExecutionRecordByData')
  async findAPIExecutionRecordByData(
    @Body('data') data: Partial<APIEntity>
  ): Promise<APIEntity[]> {
    console.log('inside automation controller', data);
    return await this.automationService.findAPIExecutionRecordByData(data);
  }

  @Get('ApptData')
  async findApptData(): Promise<ApptEntity[]> {
    console.log('inside automation controller');
    return await this.automationService.findApptData();
  }
  @Post('ApptRecordByData')
  async findApptRecordByData(
    @Body('data') data: Partial<ApptEntity>,
    @Body('apptcreationdate') date?: string,
    @Body('comparison') comparison?: 'gt' | 'lt' | 'eq',
    @Body('lobmatch') lobmatch?: string,
    @Body('clinictype') clinictype?: string
  ): Promise<ApptEntity[]> {
    console.log('inside automation controller', data);
    const lobArray = lobmatch
      ? lobmatch.replace(/'/g, '').split(',')
      : undefined;
    const clinicArray = clinictype
      ? clinictype.replace(/'/g, '').split(',')
      : undefined;
    return await this.automationService.findApptRecordByData(
      data,
      date,
      comparison,
      lobArray,
      clinicArray
    );
  }

  @Post('CreateCucumberTags')
  @UsePipes(new ValidationPipe())
  async createCucumberTags(
    @Body('data') createCucumberTagsDto: CreateCucumberTagsDto
  ): Promise<any> {
    console.log('inside automation controller', createCucumberTagsDto);
    const result = await this.automationService.createCucumberTags(
      createCucumberTagsDto
    );
    return this.automationService.buildCucumberTags(result);
  }

  @Post('CreateExecutionRecord')
  @UsePipes(new ValidationPipe())
  async createExecutionRecord(
    @Body('data') createExecutionRecordDto: CreateExecutionRecordDto
  ): Promise<AutomationEntity> {
    console.log('create execution record', createExecutionRecordDto);
    const result = await this.automationService.createExecutionRecord(
      createExecutionRecordDto
    );
    return this.automationService.buildExecutionRecord(
      result
    ) as AutomationEntity;
  }

  @Post('CreateAPIExecutionRecord')
  @UsePipes(new ValidationPipe())
  async createAPIExecutionRecord(
    @Body('data') createAPIExecutionRecordDto: CreateAPIExecutionRecordDto
  ): Promise<APIEntity> {
    console.log('create api execution record', createAPIExecutionRecordDto);
    const result = await this.automationService.createAPIExecutionRecord(
      createAPIExecutionRecordDto
    );
    return this.automationService.buildAPIExecutionRecord(result) as APIEntity;
  }
  @Post('CreateApptRecord')
  @UsePipes(new ValidationPipe())
  async createApptRecord(
    @Body('data') createApptRecordDto: CreateApptRecordDto
  ): Promise<ApptEntity> {
    console.log('create appt record', createApptRecordDto);
    const result =
      await this.automationService.createApptRecord(createApptRecordDto);
    return this.automationService.buildApptRecord(result) as ApptEntity;
  }

  @Put('UpdateExecutionRecord')
  async updateExecutionRecord(
    @Record('id') record: number,
    @Body('data') updateExecutionRecordDto: UpdateExecutionRecordDto
  ): Promise<AutomationEntity> {
    const result = await this.automationService.updateExecutionRecord(
      record,
      updateExecutionRecordDto
    );
    return this.automationService.buildExecutionRecord(
      result
    ) as AutomationEntity;
  }
  @Put('UpdateAPIExecutionRecord')
  async updateAPIExecutionRecord(
    @Record('sno') record: number,
    @Body('data') updateAPIExecutionRecordDto: UpdateAPIExecutionRecordDto
  ): Promise<APIEntity> {
    const result = await this.automationService.updateAPIExecutionRecord(
      record,
      updateAPIExecutionRecordDto
    );
    return this.automationService.buildAPIExecutionRecord(result) as APIEntity;
  }
  @Put('UpdateApptRecord')
  async updateApptRecord(
    @Record('confirmationcode') confirmationcode: string,
    @Body('data') updateApptRecordDto: UpdateApptRecordDto
  ): Promise<ApptEntity> {
    const result = await this.automationService.updateApptRecord(
      confirmationcode,
      updateApptRecordDto
    );
    return this.automationService.buildApptRecord(result) as ApptEntity;
  }

  @Post('vertexai')
  @UseInterceptors(FileInterceptor('image'))
  async callVertexAI(
    @UploadedFile() file: any, // Changed to any temporarily
    @Body() requestDto: VertexAiRequestDto
  ): Promise<any> {
    //console.log('vertex ai request', requestDto);
    //console.log('Uploaded file:', file);
    if (!file || file.mimetype !== 'image/png') {
      throw new BadRequestException('A PNG image file is required');
    }

    const result = await this.automationService.processRequest(
      file.buffer,
      requestDto.domObject,
      requestDto.prompt
    );

    return result;
  }

  @Delete('DeleteCucumberTag')
  async deleteCucumberTag(
    @Body('data') data: { cucumbertag: string }
  ): Promise<{ deleted: boolean; message: string }> {
    console.log('Deleting cucumber tag:', data.cucumbertag);
    return this.automationService.deleteCucumberTag(data.cucumbertag);
  }

  @Post('FindMatchingCucumberTags')
  async findCucumberTags(
    @Body('data') data: { cucumbertag: string }
  ): Promise<CucumberTagsEntity[]> {
    console.log('Searching for cucumber tags with pattern:', data.cucumbertag);
    return this.automationService.findCucumberTagsByPattern(data.cucumbertag);
  }
}
