import { Injectable , NotFoundException, ConflictException, BadRequestException } from "@nestjs/common";
import { Repository } from "typeorm";
import { AutomationEntity, APIEntity, ApptEntity, CucumberTagsEntity } from './automation.entity';
import { InjectRepository } from "@nestjs/typeorm";
import { CreateExecutionRecordDto, CreateAPIExecutionRecordDto, CreateApptRecordDto,CreateCucumberTagsDto } from "./dto/createExecutionRecord.dto";
import { UpdateExecutionRecordDto, UpdateAPIExecutionRecordDto, UpdateApptRecordDto, UpdateCucumberTagsDto } from "./dto/updateExecutionRecord.dto";
import { VertexAI } from '@google-cloud/vertexai';
import { GenerateContentRequest } from '@google-cloud/vertexai/build/src/types/content';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>han , Between, Equal, In, Not, <PERSON>Null, Any ,FindOptionsWhere, Like} from "typeorm";
import { format } from 'date-fns';

// import { ExecutionRecordInterface } from "./types/executionRecord.interface";
const PROJECT_ID = 'digital-dev-foundation'; 
const LOCATION = 'us-central1';
const MODEL = 'gemini-2.0-flash-001'; 


@Injectable()
export class AutomationService {
  constructor(
    @InjectRepository(AutomationEntity)
    private readonly automationRepository: Repository<AutomationEntity>,
    @InjectRepository(APIEntity)
    private readonly apiExecutionRepository: Repository<APIEntity>,
    @InjectRepository(ApptEntity)
    private readonly apptRepository: Repository<ApptEntity>,
    @InjectRepository(CucumberTagsEntity)
    private readonly cucumberTagsRepository: Repository<CucumberTagsEntity>
  ) {}

  async findAll(): Promise<AutomationEntity[]> {
    return await this.automationRepository.find();
  }

  async findCucumberTagsData(): Promise<CucumberTagsEntity[]> {
    return await this.cucumberTagsRepository.find();
  }

  async findExecutionRecordByData(
    where: Partial<AutomationEntity>
  ): Promise<AutomationEntity[]> {
    const executionRecords = await this.automationRepository.find({ where });
    if (!executionRecords || executionRecords.length === 0) {
      throw new NotFoundException(
        `Execution records not found for criteria: ${JSON.stringify(where)}`
      );
    }
    return executionRecords; //
  }

  async findAPIExecutionData(): Promise<APIEntity[]> {
    return await this.apiExecutionRepository.find();
  }

  async findAPIExecutionRecordByData(
    where: Partial<APIEntity>
  ): Promise<APIEntity[]> {
    const apiExecutionRecords = await this.apiExecutionRepository.find({
      where,
    });
    if (!apiExecutionRecords || apiExecutionRecords.length === 0) {
      throw new NotFoundException(
        `API Execution records not found for criteria: ${JSON.stringify(where)}`
      );
    }
    return apiExecutionRecords;
  }

  async findApptData(): Promise<ApptEntity[]> {
    return await this.apptRepository.find();
  }

  async findApptRecordByData(
    data: Partial<ApptEntity>,
    date?: string,
    comparison: 'gt' | 'lt' | 'eq' = 'gt',
    lobmatch?: string[],
    clinictype?: string[]
  ): Promise<ApptEntity[]> {
    const whereClause: FindOptionsWhere<ApptEntity> = { ...data };
    if (lobmatch?.length) {
      whereClause.lob = In(lobmatch);
    }
    if (clinictype?.length) {
      whereClause.clinictype = In(clinictype);
    }
    if (date) {
      //const formattedDate = format(new Date(date), 'yyyy-MM-dd HH:mm:ss');
      const [year, month, day] = date.split('-').map(Number);
      const startDate = new Date(year, month - 1, day, 0, 0, 0);
      const endDate = new Date(year, month - 1, day, 23, 59, 59, 999);

      if (comparison === 'gt') {
        whereClause.apptcreationdate = MoreThan(
          format(endDate, 'yyyy-MM-dd HH:mm:ss')
        );
      } else if (comparison === 'lt') {
        whereClause.apptcreationdate = LessThan(
          format(startDate, 'yyyy-MM-dd HH:mm:ss')
        );
      } else if (comparison === 'eq') {
        whereClause.apptcreationdate = Between(
          format(startDate, 'yyyy-MM-dd HH:mm:ss'),
          format(endDate, 'yyyy-MM-dd HH:mm:ss')
        );
      }
    }

    // const apptRecords = await this.apptRepository.find({ where: whereClause });
    const apptRecords = await this.apptRepository.find({
      where: whereClause,
      order: { apptcreationdate: 'ASC' },
    });
    console.log('apptRecords', apptRecords);
    if (!apptRecords || apptRecords.length === 0) {
      throw new NotFoundException(
        `Appt records not found for criteria: ${JSON.stringify({
          ...data,
          date,
          comparison,
          lobmatch,
          clinictype,
        })}`
      );
    }
    return apptRecords;
  }

  async createCucumberTags(
    createCucumberTagsDto: CreateCucumberTagsDto
  ): Promise<CucumberTagsEntity> {
    try {
      const cucumberTags = new CucumberTagsEntity();
      Object.assign(cucumberTags, createCucumberTagsDto);
      return await this.cucumberTagsRepository.save(cucumberTags);
    } catch (error) {
      if (error.code === '23505') {
        throw new ConflictException(
          'Duplicate key value violates unique constraint'
        );
      }
      throw error;
    }
  }

  async createExecutionRecord(
    createExecutionRecordDto: CreateExecutionRecordDto
  ): Promise<AutomationEntity> {
    try {
      const executionRecord = new AutomationEntity();
      Object.assign(executionRecord, createExecutionRecordDto);
      return await this.automationRepository.save(executionRecord);
    } catch (error) {
      if (error.code === '23505') {
        throw new ConflictException(
          'Duplicate key value violates unique constraint'
        );
      }
      throw error;
    }
  }
  async createAPIExecutionRecord(
    createAPIExecutionRecordDto: CreateAPIExecutionRecordDto
  ): Promise<APIEntity> {
    try {
      const apiExecutionRecord = new APIEntity();
      Object.assign(apiExecutionRecord, createAPIExecutionRecordDto);
      return await this.apiExecutionRepository.save(apiExecutionRecord);
    } catch (error) {
      if (error.code === '23505') {
        throw new ConflictException(
          'Duplicate key value violates unique constraint'
        );
      }
      throw error;
    }
  }
  async createApptRecord(
    createApptRecordDto: CreateApptRecordDto
  ): Promise<ApptEntity> {
    //return createExecutionRecordDto;
    const apptRecord = new ApptEntity();
    Object.assign(apptRecord, createApptRecordDto);

    return await this.apptRepository.save(apptRecord);
  }

  async updateExecutionRecord(
    record: number,
    updateExecutionRecordDto: UpdateExecutionRecordDto
  ): Promise<AutomationEntity> {
    const executionRecord = await this.automationRepository.findOne({
      where: { id: record },
    });
    if (!executionRecord) {
      throw new NotFoundException(
        `Execution record with ID ${record} not found`
      );
    }
    Object.assign(executionRecord, updateExecutionRecordDto);
    return await this.automationRepository.save(executionRecord);
  }
  async updateAPIExecutionRecord(
    record: number,
    updateAPIExecutionRecordDto: UpdateAPIExecutionRecordDto
  ): Promise<APIEntity> {
    const apiExecutionRecord = await this.apiExecutionRepository.findOne({
      where: { sno: record },
    });

    if (!apiExecutionRecord) {
      throw new NotFoundException(
        `API Execution record with ID ${record} not found`
      );
    }
    Object.assign(apiExecutionRecord, updateAPIExecutionRecordDto);
    return await this.apiExecutionRepository.save(apiExecutionRecord);
  }

  async updateApptRecord(
    confirmationcode: string,
    updateApptRecordDto: UpdateApptRecordDto
  ): Promise<ApptEntity> {
    const apptRecord = await this.apptRepository.findOne({
      where: { confirmationcode: confirmationcode },
    });

    if (!apptRecord) {
      throw new NotFoundException(
        `Appt record with confirmationcode ${confirmationcode} not found`
      );
    }
    Object.assign(apptRecord, updateApptRecordDto);
    return await this.apptRepository.save(apptRecord);
  }

  buildCucumberTags(cucumberTagsEntity: CucumberTagsEntity): any {
    return {
      data: {
        ...cucumberTagsEntity,
      },
    };
  }

  buildExecutionRecord(automationEntity: AutomationEntity): any {
    return {
      data: {
        ...automationEntity,
      },
    };
  }
  buildAPIExecutionRecord(apiEntity: APIEntity): any {
    return {
      data: {
        ...apiEntity,
      },
    };
  }
  buildApptRecord(apptEntity: ApptEntity): any {
    return {
      data: {
        ...apptEntity,
      },
    };
  }
  async processRequest(
    imageBuffer: Buffer,
    domObject: string,
    prompt: string
  ): Promise<any> {
    try {
      const imageBase64 = imageBuffer.toString('base64');

      const vertexAi = new VertexAI({
        project: PROJECT_ID,
        location: LOCATION,
      });
      const generativeModel = vertexAi.preview.getGenerativeModel({
        model: MODEL,
      });

      const mimeType = 'image/png';

      // Create a properly typed request object
      const request: GenerateContentRequest = {
        contents: [
          {
            role: 'user',
            parts: [
              {
                text: `${prompt} ${domObject}`,
              },
              {
                inlineData: {
                  mimeType: mimeType,
                  data: imageBase64,
                },
              },
            ],
          },
        ],
      };

      const response = await generativeModel.generateContent(request);
      const text =
        response?.response?.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!text) {
        throw new Error('No text generated in the response.');
      }

      return text;
    } catch (error) {
      console.error(
        'Error calling Vertex AI:',
        error.response?.data || error.message
      );
      throw error;
    }
  }

  async deleteCucumberTag(
    cucumbertag: string
  ): Promise<{ deleted: boolean; message: string }> {
    try {
      const tag = await this.cucumberTagsRepository.findOne({
        where: { cucumbertag },
      });

      if (!tag) {
        return {
          deleted: false,
          message: `No cucumber tag found with value: ${cucumbertag}`,
        };
      }

      const result = await this.cucumberTagsRepository.delete({ cucumbertag });

      const affectedRows = result.affected ?? 0;

      return {
        deleted: affectedRows > 0,
        message:
          affectedRows > 0
            ? `Successfully deleted cucumber tag: ${cucumbertag}`
            : `Failed to delete cucumber tag: ${cucumbertag}`,
      };
    } catch (error) {
      return {
        deleted: false,
        message: `Error deleting cucumber tag: ${error.message}`,
      };
    }
  }

  async findCucumberTagsByPattern(
    pattern: string
  ): Promise<CucumberTagsEntity[]> {
    if (!pattern || pattern.trim() === '') {
      throw new BadRequestException('Search pattern cannot be empty');
    }

    // Search for tags that contain the pattern
    const tags = await this.cucumberTagsRepository.find({
      where: {
        cucumbertag: Like(`%${pattern}%`),
      },
      order: {
        cucumbertag: 'ASC', 
      },
    });

    if (!tags || tags.length === 0) {
      throw new NotFoundException(
        `No cucumber tags found matching pattern: ${pattern}`
      );
    }

    return tags;
  }
}
