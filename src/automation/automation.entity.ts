import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity({ name: 'cucumbertags' })
export class CucumberTagsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  cucumbertag: string;

  @Column({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    nullable: false,
  })
  modifieddate: Date;
}

//@Entity({ name: process.env.POSTGRES_TABLE })
@Entity({ name: 'e2e_testexecutiondetails' })
export class AutomationEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  testenv: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  cc_pipelineid: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  cc_jobid: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  cc_jobname: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  workflowname: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  parenttag: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  scenariooutline: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  platform: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  status: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  failedstep: string;

  @Column({ type: 'text', nullable: true })
  failedreason: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  qmsessiondetails: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  qmsessionlink: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  failedcategory: string;

  @Column({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  execution_timestamp: Date;

  @Column({ type: 'varchar', length: 50, nullable: true })
  timetaken: string;

  @Column({ type: 'integer', nullable: true })
  totaltimeinseconds: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  triggeredby: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  artifactsurl: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  jiraissueid: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  defectid: string;
}


@Entity({ name: 'api_execution_results' })
export class APIEntity {
  @PrimaryGeneratedColumn()
  sno: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  apiname: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  status: string;

  @Column({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  testexecutiondateandtime: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  statuscode: string;

  @Column({ type: 'text', nullable: true })
  statusdesc: string;

  @Column({ type: 'text', nullable: true })
  response: string;

  @Column({ type: 'text', nullable: true })
  response_headers: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  responsetime: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  jiraissueid: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  defectid: string;
}

@Entity({ name: 'appt_details' })
export class ApptEntity {
  @PrimaryGeneratedColumn()
  sno: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  confirmationcode: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  cancellationurl: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  xid: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  apptcreationdate: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  apptdate: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  fname: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  lname: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  dob: string;

  @Column({ type: 'varchar', length: 45, nullable: true })
  lob: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  clinictype: string;

  @Column({ type: 'varchar', length: 10000, nullable: true })
  cancel_status: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  pipelineid: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  jobid: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  artifactsurl: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  createdby: string;

}
