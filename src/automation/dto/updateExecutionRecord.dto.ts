import { IsNotEmpty } from "class-validator";

export class UpdateCucumberTagsDto {
  readonly cucumbertag: string;
  readonly modifieddate: Date;
}

export class UpdateExecutionRecordDto {
  readonly testenv: string;
  readonly cc_pipelineid: string;
  readonly cc_jobid: string;
  readonly cc_jobname: string;
  readonly workflowname: string;
  readonly parenttag: string;
  readonly scenariooutline: string;
  readonly platform: string;
  readonly status: string;
  readonly failedstep: string;
  readonly failedreason: string;
  readonly qmsessiondetails: string;
  readonly qmsessionlink: string;
  readonly failedcategory: string;
  readonly execution_timestamp: Date;
  readonly timetaken: string;
  readonly totaltimeinseconds: number;
  readonly triggeredby: string;
  readonly artifactsurl: string;
  readonly jiraissueid: string;
  readonly defectid: string;
}

export class UpdateAPIExecutionRecordDto {
  readonly apiname: string;
  readonly status: string;
  readonly testexecutiondateandtime: Date;
  readonly statuscode: string;
  readonly statusdesc: string;
  readonly response: string;
  readonly response_headers: string;
  readonly responsetime: string;
  readonly jiraissueid: string;
  readonly defectid: string;
}

export class UpdateApptRecordDto {
  readonly confirmationcode: string;
  readonly cancellationurl: string;
  readonly xid: string;
  readonly apptcreationdate: string;
  readonly apptdate: string;
  readonly fname: string;
  readonly lname: string;
  readonly dob: string;
  readonly lob: string;
  readonly clinictype: string;
  readonly cancel_status: string;
  readonly pipelineid: string;
  readonly jobid: string;
  readonly artifactsurl: string;
  readonly createdby: string;
}
