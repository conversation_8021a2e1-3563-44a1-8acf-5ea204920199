import { Modu<PERSON> } from "@nestjs/common";
import { AutomationController } from './automation.controller';
import { AutomationService } from './automation.service';
import { TypeOrmModule } from "@nestjs/typeorm";
import { AutomationEntity, APIEntity, ApptEntity, CucumberTagsEntity } from './automation.entity';

@Module({
  imports: [TypeOrmModule.forFeature([AutomationEntity, APIEntity, ApptEntity, CucumberTagsEntity])],
  controllers: [AutomationController],
  providers: [AutomationService],
  //exports: [AutomationService],
})
export class AutomationModule {}
