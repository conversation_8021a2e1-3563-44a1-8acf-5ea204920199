import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { AutomationModule } from "./automation/automation.module";
import { DatabaseModule } from "./database/database.module";
import {MulterModule} from '@nestjs/platform-express';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024,
      },
    }),
    AutomationModule,
    DatabaseModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
