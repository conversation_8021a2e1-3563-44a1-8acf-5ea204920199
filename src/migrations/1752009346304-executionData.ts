import { MigrationInterface, QueryRunner } from "typeorm";

export class ExecutionData1752009346304 implements MigrationInterface {
    name = 'ExecutionData1752009346304'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "cucumbertags" ("id" SERIAL NOT NULL, "cucumbertag" character varying(255) NOT NULL, "modifieddate" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_521d2b124d5613dcc07a94f1503" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "cucumbertags"`);
    }

}
