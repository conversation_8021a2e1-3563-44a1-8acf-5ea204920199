import { Modu<PERSON> } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        type: "postgres",
        host: configService.getOrThrow("POSTGRES_HOST"),
        port: configService.getOrThrow("POSTGRES_PORT"),
        database: configService.getOrThrow("POSTGRES_DB"),
        username: configService.getOrThrow("POSTGRES_USER"),
        password: configService.getOrThrow("POSTGRES_PASSWORD"),
        // ssl: {
        //   rejectUnauthorized: false,
        // },
        extra: {
          socketPath:
            "/cloudsql/digital-dev-foundation:us-east4:health-automation",
        },
        autoLoadEntities: true,
        //synchronize: configService.getOrThrow("ENV") == "DEV" ? true : false,
        synchronize: false,
        entities: [__dirname + "/**/*.entity{.ts,.js}"],
        // logging: configService.getOrThrow('ENV') == 'DEV' ? true : false,
      }),
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule {}
