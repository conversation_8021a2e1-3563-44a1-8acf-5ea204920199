import { DataSource } from "typeorm";
import "dotenv/config";

const datasource = new DataSource({
  type: "postgres",
  host: process.env.POSTGRES_HOST,
  port: 5432,
  username: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  database: process.env.POSTGRES_DB,
  entities: [__dirname + "/**/*.entity{.ts,.js}"],
  migrations: [__dirname + "/migrations/**/*{.ts,.js}"],
  synchronize: false,
  ssl: {
    rejectUnauthorized: false,
  },
  extra: {
    socketPath:"/cloudsql/digital-dev-foundation:us-east4:health-automation",
  },
});

export default datasource;
